#!/usr/bin/env python3
"""
DBResNet深度学习训练模块 - 磁导航轨迹预测系统
======================================================

功能说明:
1. 实现基于Vision Transformer和ResNet的双路径融合架构
2. 训练深度学习模型用于磁导航轨迹误差预测
3. 支持模型测试、评估和可视化分析

核心架构:
- DBResNet4: 双路径特征融合网络
  * ViT路径: 处理RGB磁场图像序列
  * ResNet路径: 处理灰度磁场图像
  * 轨迹编码器: 处理参考轨迹坐标
  * 融合层: 多模态特征融合

训练策略:
- 随机采样训练数据以提高泛化能力
- 组合损失函数(MSE + 轨迹形状约束)
- 早停机制防止过拟合
- 定期保存检查点和最佳模型

作者: DBResNet项目组
版本: v2.0
"""

import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R
from transformers import ViTForImageClassification
from transformers import ViTModel, ViTConfig
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader, Subset, Dataset
import os
import torch.nn.functional as F
from transformers import ViTConfig
from torchvision import transforms
import time
import shutil
import datetime
import json
import re

# ====================================================================
# 全局配置参数
# ====================================================================
# 当前时间戳，用于生成唯一的文件和目录名
current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")

# 训练相关参数配置
BATCHSIZE_ = 10                     # 训练批次大小 (根据GPU显存调整)
BATCHSIZE_Test = 10                 # 测试批次大小
ALLNUM_ = 900                       # 每个epoch使用的训练样本数
ALLNUM_FOR_TRAIN = 3422            # 训练集总样本数量
Train_state = 0                     # 训练状态标志 (0:测试模式, 1:训练模式)

# 损失函数权重配置
a = 1.5                             # 基础MSE损失权重
b = 1.5                             # 轨迹形状损失权重

# 设备选择: 优先使用GPU，回退到CPU
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
if Train_state == 0:
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(f"使用设备: {device}")


def clear_save_path(save_path):
    """
    清理并重新创建保存路径
    
    参数:
        save_path: 要清理的目录路径
        
    功能:
        确保保存目录的干净状态，避免旧文件干扰
    """
    if os.path.exists(save_path):
        shutil.rmtree(save_path)  # 删除整个目录树
    os.makedirs(save_path)        # 重新创建目录


class ResidualBlock(nn.Module):
    """
    残差块(Residual Block)实现
    
    核心思想:
        通过跳跃连接(Skip Connection)解决深度网络的梯度消失问题
        
    结构:
        input -> Conv -> BN -> ReLU -> Conv -> BN -> (+shortcut) -> ReLU -> output
        
    参数:
        in_channels: 输入通道数
        out_channels: 输出通道数
        stride: 卷积步长(用于下采样)
    """
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        
        # 主要路径: 两个3x3卷积层
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, 
                              stride=stride, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()
        
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, 
                              stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        # 快捷连接(Shortcut Connection)
        # 当维度不匹配时使用1x1卷积调整
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, 
                         stride=stride, padding=0),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x):
        """
        前向传播
        
        参数:
            x: 输入特征 (batch_size, in_channels, height, width)
            
        返回:
            output: 输出特征 (batch_size, out_channels, height, width)
        """
        # 主路径
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        
        # 添加快捷连接
        out += self.shortcut(x)
        out = self.relu(out)
        
        return out


class DBResNet(nn.Module):
    """
    基础深度轨迹预测网络
    
    架构:
        - ViT编码器: 处理RGB磁场图像序列
        - 轨迹编码器: 处理参考轨迹坐标
        - 融合层: 特征融合和输出预测
        
    应用场景:
        用于单路径特征融合的轨迹预测任务
    """
    def __init__(self, num_points=8):
        super(DBResNet, self).__init__()
        
        # ViT模型: 使用预训练的Vision Transformer
        # 'google/vit-base-patch16-224-in21k' 在ImageNet-21k上预训练
        self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224-in21k')
        
        # 轨迹特征处理层
        # 输入: num_points * 2 (每个点的x,y坐标)
        # 输出: 512维特征向量
        self.fc_trajectory = nn.Linear(num_points * 2, 512)
        
        # 特征融合层
        # 输入: ViT特征(768) + 轨迹特征(512) = 1280维
        # 输出: 1024维融合特征
        self.fc_fusion = nn.Linear(768 + 512, 1024)
        
        # 输出层: 预测轨迹修正量
        # 输出: num_points * 2 (每个点的x,y修正量)
        self.fc_output = nn.Linear(1024, num_points * 2)

    def forward(self, images, trajectory):
        """
        前向传播
        
        参数:
            images: 图像序列 (batch_size, seq_len, 3, 224, 224)
            trajectory: 轨迹坐标 (batch_size, num_points*2)
            
        返回:
            output: 轨迹修正量 (batch_size, num_points, 2)
        """
        batch_size, seq_len, c, h, w = images.shape
        vit_features = []
        
        # 逐帧处理图像序列
        for t in range(seq_len):
            # ViT前向传播
            vit_output = self.vit(images[:, t, :, :, :])
            # 使用全局平均池化获得图像特征
            vit_features.append(vit_output.last_hidden_state.mean(dim=1))
        
        # 堆叠时序特征
        vit_features = torch.stack(vit_features, dim=1)  # (batch, seq_len, 768)
        
        # 轨迹特征编码
        trajectory_features = torch.relu(self.fc_trajectory(trajectory))
        
        # 时序特征聚合: 简单平均
        vit_features = vit_features.mean(dim=1)  # (batch, 768)
        
        # 特征融合
        combined_features = torch.cat([vit_features, trajectory_features], dim=1)
        fusion_features = torch.relu(self.fc_fusion(combined_features))
        
        # 输出预测
        output = self.fc_output(fusion_features)
        output = output.view(batch_size, 10, 2)  # 重塑为(batch, 10, 2)
        
        return output


class DBResNet4(nn.Module):
    """
    增强版深度轨迹预测网络 - 双路径融合架构
    
    核心创新:
        1. ViT路径: 处理RGB磁场图像，提取全局特征
        2. ResNet路径: 处理灰度磁场图像，提取局部纹理特征
        3. 轨迹编码器: 处理参考轨迹的运动模式
        4. 双路径融合: 两个并行融合分支增强特征表达能力
        
    优势:
        - 多模态特征融合提高预测精度
        - 双路径设计增强模型鲁棒性
        - 残差连接改善梯度流动
    """
    def __init__(self, num_points=8):
        super(DBResNet4, self).__init__()
        
        # ViT编码器: 处理RGB磁场图像
        self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224-in21k')
        
        # ResNet编码器: 处理灰度磁场图像
        self.resnet = nn.Sequential(
            ResidualBlock(1, 16),           # 输入1通道，输出16通道
            nn.MaxPool2d(kernel_size=2, stride=2),  # 下采样
            ResidualBlock(16, 32),          # 输入16通道，输出32通道
            nn.AdaptiveAvgPool2d((1, 1)),   # 全局平均池化到1x1
            nn.Flatten(),                   # 展平
            nn.Linear(32, 512),             # 全连接层
            nn.ReLU()                       # 激活函数
        )
        
        # 轨迹特征编码器
        self.fc_trajectory = nn.Linear(num_points * 2, 512)
        
        # 双路径融合架构
        # 路径1: ViT + 轨迹特征
        self.fc_fusion1 = nn.Linear(768 + 512, 1024)
        self.fc_output1 = nn.Linear(1024, num_points * 2)
        
        # 路径2: ViT + ResNet + 轨迹特征
        self.fc_fusion2 = nn.Linear(768 + 512 + 512, 1024)
        self.fc_output2 = nn.Linear(1024, num_points * 2)

    def forward(self, images, trajectory):
        """
        前向传播 - 双路径融合推理
        
        参数:
            images: 图像序列 (batch_size, seq_len, 3, 224, 224)
            trajectory: 轨迹坐标 (batch_size, num_points*2)
            
        返回:
            output: 轨迹修正量 (batch_size, num_points, 2)
            
        处理流程:
            1. ViT路径处理RGB图像序列
            2. ResNet路径处理灰度图像序列
            3. 编码参考轨迹特征
            4. 双路径并行融合
            5. 残差连接合并输出
        """
        batch_size, seq_len, c, h, w = images.shape
        
        # ===== ViT特征提取路径 =====
        vit_features = []
        for t in range(seq_len):
            # 对每帧RGB图像进行ViT编码
            vit_output = self.vit(images[:, t, :, :, :])
            # 使用平均池化聚合patch特征
            vit_features.append(vit_output.last_hidden_state.mean(dim=1))
        vit_features = torch.stack(vit_features, dim=1)  # (batch, seq_len, 768)
        
        # ===== ResNet特征提取路径 =====
        cnn_features = []
        # 提取第一个通道作为灰度图像 (通常是磁场强度)
        original_image = images[:, :, 0:1, :, :]  # (batch, seq_len, 1, 224, 224)
        
        for t in range(seq_len):
            # 对每帧灰度图像进行ResNet编码
            cnn_output = self.resnet(original_image[:, t, :, :, :])
            cnn_features.append(cnn_output)
        cnn_features = torch.stack(cnn_features, dim=1)  # (batch, seq_len, 512)
        
        # ===== 轨迹特征编码 =====
        trajectory_features = torch.relu(self.fc_trajectory(trajectory))
        
        # ===== 时序特征聚合 =====
        vit_features = vit_features.mean(dim=1)  # (batch, 768)
        cnn_features = cnn_features.mean(dim=1)  # (batch, 512)
        
        # ===== 双路径融合 =====
        # 路径1: ViT特征 + 轨迹特征
        combined_features1 = torch.cat([vit_features, trajectory_features], dim=1)
        fusion_features1 = torch.relu(self.fc_fusion1(combined_features1))
        output1 = self.fc_output1(fusion_features1)
        
        # 路径2: ViT特征 + ResNet特征 + 轨迹特征
        combined_features2 = torch.cat([vit_features, cnn_features, trajectory_features], dim=1)
        fusion_features2 = torch.relu(self.fc_fusion2(combined_features2))
        output2 = self.fc_output2(fusion_features2)
        
        # ===== 残差连接合并输出 =====
        output = output1.view(batch_size, 10, 2) + output2.view(batch_size, 10, 2)
        
        return output


class TrajectoryLoss(nn.Module):
    """
    轨迹形状约束损失函数
    
    设计理念:
        除了基础的MSE损失外，还需要确保预测轨迹的几何合理性
        
    损失组成:
        1. 间距一致性损失: 相邻轨迹点间距应该相对均匀
        2. 方向一致性损失: 轨迹应保持平滑的方向变化
        
    应用价值:
        - 防止轨迹出现异常的急转弯或不规则形状
        - 保持轨迹的物理合理性
        - 提高导航精度的稳定性
    """
    def __init__(self, alpha=1.0, beta=1.0):
        super(TrajectoryLoss, self).__init__()
        self.alpha = alpha  # 间距一致性权重
        self.beta = beta    # 方向一致性权重

    def forward(self, outputs):
        """
        计算轨迹形状损失
        
        参数:
            outputs: 预测的轨迹修正量 (batch_size, num_points, 2)
            
        返回:
            total_loss: 总形状损失
        """
        # ===== 间距一致性损失 =====
        # 计算相邻点之间的位移向量
        diffs = outputs[:, 1:, :] - outputs[:, :-1, :]  # (batch, num_points-1, 2)
        
        # 计算相邻点之间的欧氏距离
        distances = torch.norm(diffs, p=2, dim=2)  # (batch, num_points-1)
        
        # 计算每条轨迹的平均间距
        mean_distances = torch.mean(distances, dim=1, keepdim=True)  # (batch, 1)
        
        # 计算距离方差作为间距一致性损失
        variance = torch.mean((distances - mean_distances) ** 2, dim=1)  # (batch,)
        distance_loss = torch.mean(variance)  # 标量
        
        # ===== 方向一致性损失 =====
        # 计算相邻位移向量的变化(二阶差分)
        gradients = diffs[:, 1:, :] - diffs[:, :-1, :]  # (batch, num_points-2, 2)
        
        # 计算方向变化的幅度
        gradient_loss = torch.mean(torch.norm(gradients, p=2, dim=2) ** 2)
        
        # ===== 组合总损失 =====
        total_loss = self.alpha * distance_loss + self.beta * gradient_loss
        
        return total_loss


def eva(model, eva_loader, save_path="test_images3/", IsSHOW=0):
    """
    模型评估函数 - 生成预测结果文件
    
    参数:
        model: 要评估的模型
        eva_loader: 评估数据加载器
        save_path: 结果保存路径
        IsSHOW: 是否保存可视化图像 (0:否, 1:是)
        
    返回:
        平均欧氏距离误差
        
    功能:
        1. 对评估数据集进行推理
        2. 计算预测精度指标
        3. 生成结果文件用于后续分析
        4. 可选的可视化输出
    """

    model.eval()  # 设置为评估模式
    
    with torch.no_grad():  # 禁用梯度计算
        running_loss = 0.0
        euclidean_distance_all = 0.0

        # 确定结果文件保存路径
        results_dir = os.path.join(os.path.dirname(os.path.dirname(save_path)), "results")
        result_file = os.path.join(results_dir, 
                                  f"evaluation_results_{os.path.basename(save_path)}.txt")
        
        # 创建结果目录
        os.makedirs(results_dir, exist_ok=True)
        print(f"预测结果文件保存路径: {result_file}")
        
        with open(result_file, 'a') as result_f:
            total_samples = len(eva_loader)
            for i, (images, trajectories, targets, path) in enumerate(eva_loader):
                # 显示评估进度
                if (i + 1) % 100 == 0 or i == 0 or (i + 1) == total_samples:
                    print(f"评估进度: {i + 1}/{total_samples} ({100 * (i + 1) / total_samples:.1f}%)")
                # 从文件路径提取文件名
                match = re.search(r'([^/]+)\.pth$', path[0])
                if match:
                    file_name = match.group(1)

                # 数据移动到GPU
                images = images.to(device)
                trajectories = trajectories.to(device)
                targets = targets.to(device)
                
                # 模型推理
                start_time = time.time()
                outputs = model(images, trajectories)
                end_time = time.time()
                
                # 计算推理时间
                inference_time = end_time - start_time
                
                # 计算轨迹形状损失
                traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)
                shape_loss = traj_loss(trajectories.view(-1, 10, 2) - outputs)
                
                # 计算预测精度(欧氏距离)
                euclidean_distance = torch.norm(
                    outputs.view(-1, 10, 2) - targets.view(-1, 10, 2), 
                    p=2, dim=2
                )
                euclidean_distance_all += torch.mean(torch.mean(euclidean_distance, dim=1).cpu())
                
                # 计算轨迹修正量并写入结果文件
                diff = (trajectories.view(-1, 10, 2) - outputs).cpu().numpy()
                
                # 写入结果: 文件名 + 10个点的x,y修正量 (共20个数值)
                result_line = f"{file_name}"
                for point_idx in range(10):
                    result_line += f" {diff[0][point_idx, 0]} {diff[0][point_idx, 1]}"
                result_line += "\n"
                result_f.write(result_line)

                # 可选的可视化输出
                if IsSHOW:
                    # 保存轨迹对比图
                    for j in range(images.shape[0]):
                        plt.figure(figsize=(8, 6))
                        
                        # 获取轨迹数据
                        true_trajectory = targets[j].cpu().numpy().reshape(-1, 2)
                        noisy_trajectory = trajectories[j].cpu().numpy().reshape(-1, 2)
                        predicted_trajectory = outputs[j].cpu().numpy()
                        
                        # 绘制三种轨迹
                        plt.plot(noisy_trajectory[:, 0] - true_trajectory[:, 0], 
                                noisy_trajectory[:, 1] - true_trajectory[:, 1], 
                                'bo-', label="真实误差")
                        plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 
                                'go-', label="观测轨迹")
                        plt.plot(noisy_trajectory[:, 0] - predicted_trajectory[:, 0], 
                                noisy_trajectory[:, 1] - predicted_trajectory[:, 1], 
                                'ro-', label="预测修正后")
                        
                        plt.title("轨迹对比: 真实 vs 预测 vs 观测")
                        plt.xlabel("X坐标")
                        plt.ylabel("Y坐标")
                        plt.legend()

                        # 保存轨迹图
                        image_filename = os.path.join(save_path, 
                                                    f"_trajectory_{i*images.shape[0] + j}.png")
                        plt.savefig(image_filename)
                        plt.close()
                    
                    # 保存输入磁场图像
                    for j in range(images.shape[0]):
                        image_j = images[j].cpu().numpy()
                        for o in range(image_j.shape[0]):    
                            image_filename = os.path.join(save_path, 
                                                        f"_image_{i*images.shape[0] + j}_{o}.png")
                            image_o = image_j[o].transpose(1, 2, 0)  # CHW -> HWC
                            plt.imshow(image_o)
                            plt.axis('off')
                            plt.savefig(image_filename)
                            plt.close()
            
            print(f"评估完成，平均误差: {euclidean_distance_all / len(eva_loader)}")
            
        return euclidean_distance_all / len(eva_loader)


def test(model, test_loader, save_path="test_images3/", IsSHOW=0):
    """
    模型测试函数 - 详细性能分析
    
    参数:
        model: 要测试的模型
        test_loader: 测试数据加载器
        save_path: 结果保存路径
        IsSHOW: 是否保存可视化图像
        
    返回:
        平均测试损失
        
    功能:
        1. 计算多种评估指标
        2. 分析推理性能
        3. 生成详细的误差统计
        4. 可视化测试结果
    """
    model.eval()
    with torch.no_grad():
        running_loss = 0.0
        euclidean_distance_all = 0
        euclidean_distance_all_w = 0  # 加权欧氏距离

        for i, (images, trajectories, targets, _) in enumerate(test_loader):
            # 数据预处理
            images = images.to(device)
            trajectories = trajectories.to(device)
            targets = targets.to(device)
            
            # 模型推理和性能测量
            start_time = time.time()
            outputs = model(images, trajectories)
            end_time = time.time()
            
            inference_time = end_time - start_time
            print(f'单次推理时间: {inference_time:.6f} 秒')
            
            # 计算各种损失
            traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)
            shape_loss = traj_loss(trajectories.view(-1, 10, 2) - outputs)
            
            # 计算标准欧氏距离误差
            outputs_reshaped = outputs.view(-1, 10, 2)
            targets_reshaped = targets.view(-1, 10, 2)
            euclidean_distance = torch.norm(outputs_reshaped - targets_reshaped, p=2, dim=2)
            euclidean_distance_all += torch.mean(torch.mean(euclidean_distance, dim=1).cpu())
            
            # 计算加权欧氏距离误差(转换为实际地理距离)
            diff = outputs_reshaped - targets_reshaped
            diff_weighted = torch.zeros_like(diff)
            diff_weighted[..., 0] = diff[..., 0] * 100.0      # X方向权重(米/像素)
            diff_weighted[..., 1] = diff[..., 1] * 132.17     # Y方向权重(米/像素)
            euclidean_distance_w = torch.norm(diff_weighted, p=2, dim=2)
            euclidean_distance_all_w += torch.mean(torch.mean(euclidean_distance_w, dim=1).cpu())
            
            # 计算组合损失
            outputs2 = outputs.view(-1, 20)
            targets = targets.view(-1, 20)
            base_loss = criterion(outputs2, targets)
            loss = a * base_loss + b * shape_loss
            running_loss += loss.item()
            
            # 可视化测试结果
            if IsSHOW:
                # 保存轨迹对比图
                for j in range(images.shape[0]):
                    plt.figure(figsize=(8, 6))
                    
                    true_trajectory = targets[j].cpu().numpy().reshape(-1, 2)
                    noisy_trajectory = trajectories[j].cpu().numpy().reshape(-1, 2)
                    predicted_trajectory = outputs[j].cpu().numpy()
                    
                    # 绘制轨迹对比
                    plt.plot(noisy_trajectory[:, 0] - true_trajectory[:, 0], 
                            noisy_trajectory[:, 1] - true_trajectory[:, 1], 
                            'bo-', label="真实轨迹")
                    plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 
                            'go-', label="噪声轨迹")
                    plt.plot(noisy_trajectory[:, 0] - predicted_trajectory[:, 0], 
                            noisy_trajectory[:, 1] - predicted_trajectory[:, 1], 
                            'ro-', label="预测轨迹")
                    
                    plt.title("真实轨迹 vs 预测轨迹 vs 噪声轨迹")
                    plt.xlabel("X坐标")
                    plt.ylabel("Y坐标")
                    plt.legend()
                    
                    image_filename = os.path.join(save_path, 
                                                f"_trajectory_{i*images.shape[0] + j}.png")
                    plt.savefig(image_filename)
                    plt.close()
                
                # 保存输入图像
                for j in range(images.shape[0]):
                    image_j = images[j].cpu().numpy()
                    for o in range(image_j.shape[0]):    
                        image_filename = os.path.join(save_path, 
                                                    f"_image_{i*images.shape[0] + j}_{o}.png")
                        image_o = image_j[o].transpose(1, 2, 0)
                        plt.imshow(image_o)
                        plt.axis('off')
                        plt.savefig(image_filename)
                        plt.close()
        
        # 输出性能统计
        print(f"平均像素距离误差: {euclidean_distance_all / len(test_loader)}")
        print(f"加权欧氏距离误差(米): {euclidean_distance_all_w / len(test_loader)}")
        
        return running_loss / len(test_loader)


def train(model, dataloader, dataloader_test, criterion, optimizer, root_path, epochs=1, num=0):
    """
    模型训练函数 - 核心训练逻辑
    
    参数:
        model: 要训练的模型
        dataloader: 训练数据加载器
        dataloader_test: 测试数据加载器
        criterion: 基础损失函数(MSE)
        optimizer: 优化器
        root_path: 结果保存根路径
        epochs: 训练轮数
        num: 起始epoch编号(用于继续训练)
        
    返回:
        best_weight: 最佳模型权重文件路径
        
    训练策略:
        1. 随机采样训练数据以提高泛化能力
        2. 组合损失函数(基础损失 + 形状损失)
        3. 早停机制保存最佳模型
        4. 定期保存检查点
        5. 实时记录训练日志
    """
    model.train()  # 设置为训练模式
    best_loss = float('inf')
    best_weight = ""
    
    # 训练参数
    BATCHSIZE = BATCHSIZE_
    ALLNUM = ALLNUM_
    IFUPDATA = 0  # 是否更新标志
    
    # 创建保存路径
    root_save_path_weights = os.path.join(root_path, "save_weights/")
    log_file_path = os.path.join(root_path, "logs/", 
                                f"train_loss_and_test_loss_{current_time}.txt")
    
    print(f"训练日志保存路径: {log_file_path}")
    
    # 训练循环
    with open(log_file_path, "a") as log_file:
        # 写入日志文件头
        log_file.write("Epoch, Train Loss, Test Loss, Base Loss, Shape Loss\n")
        
        for epoch in range(epochs):
            running_loss = 0.0
            base_loss_sum = 0.0
            shape_loss_sum = 0.0
            
            # ===== 随机采样训练数据 =====
            # 从总训练集中随机选择ALLNUM个样本
            random_indices = set(random.sample(range(ALLNUM_FOR_TRAIN), ALLNUM))
            random_indices_list = list(random_indices)
            subset = Subset(dataloader.dataset, random_indices_list)
            
            if IFUPDATA == 0:
                subset_loader = DataLoader(subset, batch_size=BATCHSIZE, 
                                         num_workers=32, prefetch_factor=1, shuffle=False)
            
            # ===== 训练批次循环 =====
            i = 0
            subset_iterator = iter(subset_loader)
            
            for _ in range(len(subset_loader)):
                # 获取一个批次的数据
                images, trajectories, targets, _ = next(subset_iterator)
                print(f"Epoch:{epoch+1}/{epochs}, Batch:{i+1}/{len(subset_loader)}")
                i += 1
                
                # 数据移动到GPU
                images = images.to(device)
                trajectories = trajectories.to(device)
                targets = targets.to(device)
                
                # ===== 前向传播 =====
                optimizer.zero_grad()  # 清空梯度
                outputs = model(images, trajectories)
                
                # ===== 损失计算 =====
                # 轨迹形状损失
                traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)
                shape_loss = traj_loss(trajectories.view(-1, 10, 2) - outputs)
                
                # 基础MSE损失
                outputs = outputs.view(-1, 20)
                targets = targets.view(-1, 20)
                base_loss = criterion(outputs, targets)
                
                # 组合损失
                loss = a * base_loss + b * shape_loss
                
                # ===== 反向传播和优化 =====
                loss.backward()
                optimizer.step()
                
                # ===== 损失统计 =====
                base_loss_sum += base_loss.item()
                shape_loss_sum += shape_loss.item()
                running_loss += loss.item()
            
            # ===== Epoch结束处理 =====
            # 计算平均损失
            avg_train_loss = running_loss / (ALLNUM / BATCHSIZE)
            avg_base_loss = base_loss_sum / (ALLNUM / BATCHSIZE)
            avg_shape_loss = shape_loss_sum / (ALLNUM / BATCHSIZE)
            
            print(f"Epoch {epoch+1}/{epochs}, 训练损失: {avg_train_loss:.4f}")
            
            # 测试模型性能
            test_loss = test(model, dataloader_test, IsSHOW=0)
            
            # 记录训练日志
            log_file.write(f"{epoch + num + 1}, {avg_train_loss:.4f}, {test_loss:.4f}, "
                          f"{avg_base_loss:.4f}, {avg_shape_loss:.4f}\n")
            log_file.flush()  # 立即写入磁盘
            
            # ===== 检查点保存 =====
            if (epoch + 1) % 10 == 0:
                checkpoint_path = os.path.join(root_save_path_weights, 
                                             f"checkpoint_epoch_{epoch+num+1}.pth")
                torch.save({
                    'epoch': epoch + num + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'test_loss': test_loss,
                }, checkpoint_path)
                print(f"检查点已保存: {checkpoint_path}")
            
            # ===== 最佳模型保存 =====
            if test_loss < best_loss:
                IFUPDATA = 1
                best_loss = test_loss
                
                # 清理旧的权重文件
                clear_save_path(root_save_path_weights)
                
                # 生成新的最佳权重文件名
                dataset_name = (root_path.split('/')[-2] if root_path.endswith('/') 
                              else root_path.split('/')[-1])
                best_weight = os.path.join(root_save_path_weights, 
                                         f"best_model_{dataset_name}_epoch_{epoch+num+1}"
                                         f"_loss_{best_loss:.4f}.pth")
                
                # 保存最佳模型
                torch.save(model.state_dict(), best_weight)
                print(f"最佳模型已保存 - Epoch {epoch+1+num}, 测试损失: {test_loss:.4f}")
            else:
                IFUPDATA = 0
    
    return best_weight


def resize_binary_image_manual_torch(images, target_size):
    """
    手动调整二值图像大小的PyTorch实现
    
    参数:
        images: 输入图像张量
        target_size: 目标尺寸 (height, width)
        
    返回:
        resized_image: 调整大小后的图像张量
        
    功能:
        专门处理二值图像(0和1)的缩放，保持二值特性
    """
    # 处理输入格式
    if images.shape[0] == 1:
        image = images[0, :, :]
    else:
        image = images
    
    if isinstance(image, np.ndarray):
        image = torch.tensor(image)
    
    original_height, original_width = image.shape
    target_height, target_width = target_size
    
    # 初始化目标图像
    resized_image = torch.zeros((3, target_height, target_width), dtype=torch.float32)
    
    # 计算缩放比例
    scale_y = target_height / original_height
    scale_x = target_width / original_width
    
    # 处理值为1的像素
    ones_indices = torch.nonzero(image == 1, as_tuple=False)
    
    for idx in ones_indices:
        x, y = idx
        new_y = int(y.item() * scale_y)
        new_x = int(x.item() * scale_x)
        
        # 边界检查
        if 0 <= new_y < target_height and 0 <= new_x < target_width:
            resized_image[:, new_x, new_y] = 1
    
    return resized_image


class TrajectoryDataset(Dataset):
    """
    轨迹数据集类 - PyTorch Dataset实现
    
    功能:
        从.pth文件加载预处理的训练样本，包括:
        - 磁场图像序列
        - 参考轨迹坐标
        - 目标误差向量
        
    数据格式:
        - images: (10, 224, 224) -> (10, 3, 224, 224) 扩展为RGB
        - trajectory: (20,) 轨迹坐标展平
        - target: (20,) 目标误差展平
    """
    def __init__(self, data_dir):
        """
        初始化数据集
        
        参数:
            data_dir: 包含.pth文件的数据目录
        """
        self.data_dir = data_dir
        
        # 获取所有.pth文件并排序
        self.file_list = sorted([
            os.path.join(data_dir, file) 
            for file in os.listdir(data_dir) 
            if file.endswith(".pth")
        ])
        
        self.num_files = len(self.file_list)
        print(f"数据集包含 {self.num_files} 个样本")

    def __len__(self):
        """返回数据集大小"""
        return len(self.file_list)

    def __getitem__(self, idx):
        """
        获取单个样本
        
        参数:
            idx: 样本索引
            
        返回:
            images_expanded: RGB图像序列 (10, 3, 224, 224)
            trajectory: 轨迹坐标 (20,)
            target: 目标误差 (20,)
            file_path: 文件路径
        """
        # 加载.pth文件
        data_dict = torch.load(self.file_list[idx])
        
        # 提取数据
        images = data_dict['images']        # (10, 224, 224)
        trajectory = data_dict['trajectory'] # (20,)
        target = data_dict['target']        # (20,)
        file_path = self.file_list[idx]     # 文件路径
        
        # 将单通道图像扩展为RGB格式
        # (10, 224, 224) -> (10, 1, 224, 224) -> (10, 3, 224, 224)
        images_expanded = images.unsqueeze(1).repeat(1, 3, 1, 1)
        
        return images_expanded, trajectory, target, file_path


def load_data(data_dir, batch_size):
    """
    创建数据加载器
    
    参数:
        data_dir: 数据目录路径
        batch_size: 批次大小
        
    返回:
        dataloader: PyTorch DataLoader对象
    """
    dataset = TrajectoryDataset(data_dir)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    return dataloader


# ====================================================================
# 主程序
# ====================================================================
if __name__ == "__main__":
    print("=" * 60)
    print("DBResNet磁导航轨迹预测系统")
    print("=" * 60)
    
    # ===== 训练参数配置 =====
    epochs_num = 15000              # 最大训练轮数
    batch_size_train = BATCHSIZE_   # 训练批次大小
    batch_size_test = BATCHSIZE_Test # 测试批次大小
    batch_size_eva = 1              # 评估批次大小

    # ===== 数据路径配置 =====
    # 9号架次数据路径
    data_dir1 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/train"  # 训练集
    data_dir2 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/test"   # 测试集
    data_dir3 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/eva"    # 评估集

    # ===== 创建数据加载器 =====
    print("正在加载数据...")
    train_loader = load_data(data_dir1, batch_size_train)
    test_loader = load_data(data_dir2, batch_size_test)
    eva_loader = load_data(data_dir3, batch_size_eva)
    print("数据加载完成")

    # ===== 模型初始化 =====
    print("正在初始化模型...")
    model = DBResNet4(num_points=10).to(device)
    
    # 计算模型参数量
    def count_parameters(model):
        """计算模型总参数数量"""
        return sum(p.numel() for p in model.parameters())
    
    total_params = count_parameters(model)
    print(f'模型总参数量: {total_params:,}')

    # ===== 损失函数和优化器 =====
    criterion = nn.MSELoss()  # 均方误差损失
    optimizer = optim.Adam(model.parameters(), lr=0.001)  # Adam优化器

    # ===== 加载预训练权重(可选) =====
    k = 0  # 起始epoch编号
    if 1:  # 1:加载预训练权重, 0:从头开始训练
        checkpoint_path = ("data_output/model_results/yk_9_err1/save_weights/3000/"
                          "epoch=1265-step=184836.pth")
        try:
            checkpoint = torch.load(checkpoint_path, map_location=device)
            model.load_state_dict(checkpoint)
            k = 7954  # 设置起始epoch编号
            print(f"成功加载预训练权重: {checkpoint_path}")
            print(f"从第 {k} 个epoch继续训练")
        except FileNotFoundError:
            print(f"警告: 未找到权重文件 {checkpoint_path}")
            print("将从头开始训练")

    # ===== 动态设置保存路径 =====
    # 根据数据路径自动确定结果保存路径
    if "data_yk_no9_err1" in data_dir1:
        root_path = "data_output/model_results/yk_9_err1/"
    elif "data_yk_no6_err1" in data_dir1:
        root_path = "data_output/model_results/yk_6_err1/"
    elif "data_yk_no7_err1" in data_dir1:
        root_path = "data_output/model_results/yk_7_err1/"
    elif "data_yk_no8_err1" in data_dir1:
        root_path = "data_output/model_results/yk_8_err1/"
    elif "data_yk_no11_err1" in data_dir1:
        root_path = "data_output/model_results/yk_11_err1/"
    elif "data_yk_no12_err1" in data_dir1:
        root_path = "data_output/model_results/yk_12_err1/"
    elif "data_yk_no14_err1" in data_dir1:
        root_path = "data_output/model_results/yk_14_err1/"
    else:
        # 默认路径，包含时间戳
        root_path = f"data_output/model_results/experiment_{current_time}/"
    
    # ===== 创建目录结构 =====
    print(f"结果保存路径: {root_path}")
    os.makedirs(root_path, exist_ok=True)
    
    save_weights_path = os.path.join(root_path, "save_weights/")
    save_images_path = os.path.join(root_path, "save_images/")
    save_logs_path = os.path.join(root_path, "logs/")
    
    os.makedirs(save_weights_path, exist_ok=True)
    os.makedirs(save_images_path, exist_ok=True)
    os.makedirs(save_logs_path, exist_ok=True)

    # ===== 保存配置信息 =====
    config_file_path = os.path.join(save_logs_path, f"config_{current_time}.json")
    config_info = {
        "dataset": data_dir1.split('/')[-3] if '/' in data_dir1 else "unknown",
        "batch_size": BATCHSIZE_,
        "batch_size_test": BATCHSIZE_Test,
        "learning_rate": 0.001,
        "epochs": epochs_num,
        "model_type": "DBResNet4",
        "num_points": 10,
        "data_paths": {
            "train": data_dir1,
            "eval": data_dir2,
            "test": data_dir3
        },
        "timestamp": current_time,
        "device": str(device),
        "total_parameters": total_params,
        "loss_weights": {
            "base_loss_weight": a,
            "shape_loss_weight": b
        }
    }
    
    with open(config_file_path, 'w') as f:
        json.dump(config_info, f, indent=4)
    print(f"配置信息已保存: {config_file_path}")
    
    # ===== 训练或测试模式 =====
    if Train_state == 1:
        print("\n开始模型训练...")
        print("-" * 50)
        
        # 开始训练
        best_weight = train(model, train_loader, test_loader, criterion, 
                           optimizer, root_path, epochs=epochs_num, num=k)
        
        # 加载最佳权重
        if best_weight:
            checkpoint_best_weight = torch.load(best_weight)
            model.load_state_dict(checkpoint_best_weight)
            print(f"已加载最佳权重: {best_weight}")
    else:
        print("\n测试模式 - 跳过训练")

    # ===== 模型评估 =====
    print("\n开始模型评估...")
    print("-" * 50)
    
    # 创建评估结果保存路径
    save_path_ = os.path.join(root_path, "evaluation_results", current_time)
    os.makedirs(save_path_, exist_ok=True)
    
    print(f"评估结果保存路径: {save_path_}")
    
    # 执行评估
    test_loss = eva(model, eva_loader, save_path=save_path_, IsSHOW=0)
    
    print("\n评估完成!")
    print("=" * 60)