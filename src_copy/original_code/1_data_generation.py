import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from scipy.ndimage import gaussian_filter
from scipy.ndimage import binary_dilation
import random
import torch


NUM=0 


file_paths = [
    #=================6============
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\6#_pure_INS_optimized_1114\ins_pure_6_cut.csv',
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_6_2_cut.csv'
    #r'ins_pure_6_cut.csv',
    #r'avp_6_2_cut.csv' 

    #=================7============
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\7#_pure_INS_optimized_1114\ins_pure_7_cut.csv',
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_7_2_cut.csv'

    #=================8============
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\8#_pure_INS_optimized_1114\ins_pure_8_cut.csv',
    #r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_8_cut.csv'

    #=================9============
    r'data_input/20241106_ykguiji/惯导数据/纯惯导/9#_pure_INS_optimized_1113/ins_pure_avp_9_whole_cut.csv',
    r'data_input/20241106_ykguiji/惯导数据/惯卫融合用作惯导/avp_9_2_cut.csv'
]

#file_path2 = r'altitude_3000_3D_in_zrc20250116_rm_direction_diff_aircraft_number_6.csv'
#file_path2 = r'fenkuan_map/altitude_3000_3D_in_zrc20250116_rm_direction_diff_aircraft_number_6.csv'
file_path2 = r'data_input/fenkuan_map/20250312-210047572288_OK/altitude_3000_3D_in_zrc20250306_filt_move_average_w21_rm_direction_diff_aircraft_number_9.csv'

#file_path2 = r'altitude_3000_3D_in_zrc20250116_rm_direction_diff_aircraft_number_7.csv'
#file_path2 = r'altitude_3000_3D_in_zrc20250116_rm_direction_diff_aircraft_number_8.csv'
#file_path2 = r'altitude_3000_3D_in_zrc20250116_rm_direction_diff_aircraft_number_9.csv'

#file_path = r'merge_pred_3000m_3D_in_reso_25m_spline_model_LSQSp_order_3_random_seed_62662_20250120-143338402097_20250120-143549580910_and_LSQSp_20250124-091746263634_20250124-095735520342.npz'
#file_path = r'fenkuan_map/pred_3000m_3D_in_reso_25m_model_OK_20250226-170753930637_20250228-173337862304.npz'
file_path = r'data_input/fenkuan_map/20250312-210047572288_OK/pred_3000m_3D_in_reso_25m_model_OK_20250312-210047572288_20250312-212703783073.npz'

#file_path = r'new_pred_3000m_3D_out_reso_25m_kriging_model_U_cv_5_order_2_p_1_random_seed_62662_20241225-030737693426_20241225-072834827081.npz'
#file_path = r'pred_3000m_3D_out_reso_25m_kriging_model_O_cv_5_order_2_p_1_random_seed_62662_20241104-090928811403_20241104-103042251029.npz'

# 指定要筛选的时间段
#===================#6===================
#start_time = '2024-09-01 13:40:50'
#end_time = '2024-09-01 16:30:20'

#start_time = '2024-09-01 13:48:10'
#end_time = '2024-09-01 16:21:40'
#===================#7===================
#start_time = '2024-09-08 14:10:50'
#end_time = '2024-09-08 16:10:20'

#start_time = '2024-09-08 14:21:30'
#end_time = '2024-09-08 16:00:30'
#===================#8===================
#start_time = '2024-09-09 05:30:30'
#end_time = '2024-09-09 06:40:00'

#start_time = '2024-09-09 05:42:10'
#end_time = '2024-09-09 06:36:00'
#===================#9===================
#start_time = '2024-09-09 14:10:00'
#end_time = '2024-09-09 16:10:00'

start_time = '2024-09-09 14:13:00'
end_time = '2024-09-09 15:53:00'





def map_to_fixed_values(data, thresholds, values):
    """
    Map input data to fixed values based on thresholds.
    - data: Input array to be mapped
    - thresholds: List of threshold values (must be in ascending order)
    - values: List of fixed values to map to (same length as thresholds + 1)
    """
    mapped_data = np.zeros_like(data)
    for i, threshold in enumerate(thresholds):
        if i == 0:
            mapped_data[data <= threshold] = values[i]
        else:
            mapped_data[(data > thresholds[i-1]) & (data <= threshold)] = values[i]
    mapped_data[data > thresholds[-1]] = values[-1]
    return mapped_data


def load_and_plot_magnetic_field(file_path):
    """
    加载并绘制磁场图,使用三分量磁场数据作为RGB颜色上色

    参数:
        file_path: .npz文件路径
    """
    # 加载数据
    data = np.load(file_path)
    
    lat = data['lat']  # 纬度数组
    lon = data['lon']  # 经度数组
    magnetic_components = data['data']
    print(lat.shape)
    mag_x_ = magnetic_components[:, :, 0]
    mag_y_ = magnetic_components[:, :, 1]
    mag_z_ = magnetic_components[:, :, 2]
    # 归一化磁场分量以适应RGB颜色范围 [0, 1]
    def normalize(data):
        """Normalize the input array to range [0, 1]"""
        return (data - np.min(data)) / (np.max(data) - np.min(data))
    mag_x_normalized = normalize(mag_x_)
    mag_y_normalized = normalize(mag_y_)
    mag_z_normalized = normalize(mag_z_)
    # 过滤操作（高斯滤波）
    def apply_gaussian_filter(data, sigma=1):
        """Apply Gaussian filter to smooth data."""
        return gaussian_filter(data, sigma=sigma)
    # 对每个通道进行高斯滤波
    mag_x_filtered = apply_gaussian_filter(mag_x_normalized, sigma=2)
    mag_y_filtered = apply_gaussian_filter(mag_y_normalized, sigma=2)
    mag_z_filtered = apply_gaussian_filter(mag_z_normalized, sigma=2)
    thresholds = np.linspace(0, 1, 21)[1:-1]  # 10 个区间的边界值 (去掉开头和结尾)
    values = np.linspace(0.1, 1.0, 20)        # 10 个固定映射值
    # 对每个过滤后的数据进行映射
    mag_x_mapped = map_to_fixed_values(mag_x_filtered, thresholds, values)
    mag_y_mapped = map_to_fixed_values(mag_y_filtered, thresholds, values)
    mag_z_mapped = map_to_fixed_values(mag_z_filtered, thresholds, values)
    # 创建RGB颜色矩阵
    rgb_matrix = np.stack([mag_x_mapped, mag_y_mapped, mag_z_mapped], axis=-1)
    #mag_z_mapped[20:30,:]=0
    #mag_z_mapped[:,50:60]=0

    '''
    # 创建网格用于绘图
    #lon_grid, lat_grid = np.meshgrid(lon, lat)
    lon_grid=lon
    lat_grid=lat
    
    # 绘制图像
    plt.figure(figsize=(12, 8))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], origin='lower', cmap='jet')
    plt.colorbar(label='Normalized Magnetic Field Components')
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    plt.title('Magnetic Field Visualization')
    plt.grid(True)

    # 保存图片
    #current_dir = os.path.dirname(os.path.abspath(__file__))
    #save_path = os.path.join(current_dir, 'magnetic_field_visualization.png')
    #plt.savefig(save_path, dpi=300, bbox_inches='tight')

    # 显示图像
    plt.show()
    '''
    return mag_z_mapped,lon,lat,mag_x_,mag_y_,mag_z_
  



def read_XYZ_trajectory(file_path, start_time=None, end_time=None):
    """
    读取INS轨迹数据, 并根据给定的时间段筛选数据

    参数:
        file_path: CSV文件路径
        start_time: 筛选开始时间 (可选)
        end_time: 筛选结束时间 (可选)
    返回:
        DataFrame: 包含处理后的轨迹数据
    """
    # 定义列名
    columns = ['Time', 'Lat', 'Lon', 'Alt', 
              'X', 'Y', 'Z', 
              'DeltaTotalfield', 'FlightDirection', 'AircraftNumber']

    # 读取CSV文件
    df = pd.read_csv(file_path, names=columns,skiprows=1)

    # 将time列转换为datetime类型
    df['Time'] = pd.to_datetime(df['Time'])

    # 如果提供了时间和结束时间，则筛选数据
    if start_time and end_time:
        start_time = pd.to_datetime(start_time)
        end_time = pd.to_datetime(end_time)
        df = df[(df['Time'] >= start_time) & (df['Time'] <= end_time)]
    #print(df.shape)
    #print(df['time'].iloc[0])
    #print(df['time'].iloc[df.shape[0]-1])
    return df


def read_ins_trajectory(file_path, start_time=None, end_time=None):
    """
    读取INS轨迹数据,并根据给定的时间段筛选数据

    参数:
        file_path: CSV文件路径
        start_time: 筛选开始时间 (可选)
        end_time: 筛选结束时间 (可选)
    返回:
        DataFrame: 包含处理后的轨迹数据
    """
    # 定义列名
    columns = ['time', 'pitch', 'roll', 'yaw', 
              'v_e', 'v_n', 'v_u', 
              'lat', 'lon', 'alt']
    # 读取CSV文件
    df = pd.read_csv(file_path, names=columns)
    # 将time列转换为datetime类型
    df['time'] = pd.to_datetime(df['time'])
    # 如果提供了时间和结束时间，则筛选数据
    if start_time and end_time:
        start_time = pd.to_datetime(start_time)
        end_time = pd.to_datetime(end_time)
        df = df[(df['time'] >= start_time) & (df['time'] <= end_time)]
    # 将弧度转换为度
    df['lat_deg'] = np.degrees(df['lat'])
    df['lon_deg'] = np.degrees(df['lon'])
    #print(df.shape)
    #print(df['time'].iloc[0])
    #print(df['time'].iloc[df.shape[0]-1])
    return df


# 定义计算两点之间的距离（Haversine 公式）
def haversine(lat1, lon1, lat2, lon2):
    R = 6371e3  # 地球半径，单位：米
    phi1 = np.radians(lat1)
    phi2 = np.radians(lat2)
    delta_phi = np.radians(lat2 - lat1)
    delta_lambda = np.radians(lon2 - lon1)
    a = np.sin(delta_phi / 2) ** 2 + np.cos(phi1) * np.cos(phi2) * np.sin(delta_lambda / 2) ** 2
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    return R * c  # 返回距离，单位：米


def Calculatingbias_internal(data1_lon,data1_lat,data2_lon,data2_lat):
    #计算两组数据内部每隔 skip 个点的距离，以及经纬度方向上的距离偏差
    SKIP = 1000  # 跳过点数
    # 确保输入数据为 numpy 数组
    data1_lon = np.asarray(data1_lon)
    data1_lat = np.asarray(data1_lat)
    data2_lon = np.asarray(data2_lon)
    data2_lat = np.asarray(data2_lat)
    # 计算每隔 skip 个点的距离
    def compute_skip_distances(lat, lon, skip):
        lat1, lat2 = lat[:-skip], lat[skip:]
        lon1, lon2 = lon[:-skip], lon[skip:]
        distances = [
            haversine(lat1[i], lon1[i], lat2[i], lon2[i])
            for i in range(len(lat1))
        ]
        return np.array(distances)
    # 计算经纬度方向的距离偏差
    def compute_skip_differences(lat, lon, skip):
        lat1, lat2 = lat[:-skip], lat[skip:]
        lon1, lon2 = lon[:-skip], lon[skip:]
        delta_lat = [
            haversine(lat1[i], lon1[i], lat2[i], lon1[i])  # 纬度方向
            for i in range(len(lat1))
        ]
        delta_lon = [
            haversine(lat1[i], lon1[i], lat1[i], lon2[i])  # 经度方向
            for i in range(len(lon1))
        ]
        return np.array(delta_lon), np.array(delta_lat)
    # 计算每隔 skip 点的距离
    data1_skip_distances = compute_skip_distances(data1_lat, data1_lon, SKIP)
    data2_skip_distances = compute_skip_distances(data2_lat, data2_lon, SKIP)
    # 计算每隔 skip 点在经度和纬度方向的距离偏差
    data1_skip_dx, data1_skip_dy = compute_skip_differences(data1_lat, data1_lon, SKIP)
    data2_skip_dx, data2_skip_dy = compute_skip_differences(data2_lat, data2_lon, SKIP)

    # 打印统计信息
    print(f"Data1 - Max Distance (skip={SKIP}): {np.max(data1_skip_distances):.2f} m, "
          f"Min Distance: {np.min(data1_skip_distances):.2f} m, "
          f"Mean Distance: {np.mean(data1_skip_distances):.2f} m")
    print(f"Data2 - Max Distance (skip={SKIP}): {np.max(data2_skip_distances):.2f} m, "
          f"Min Distance: {np.min(data2_skip_distances):.2f} m, "
          f"Mean Distance: {np.mean(data2_skip_distances):.2f} m")
    # 绘制统计曲线图
    plt.figure(figsize=(16, 10))
    # 每隔 skip 点的距离
    plt.subplot(3, 1, 1)
    plt.plot(data1_skip_distances, label='Data1 Skip Distance', color='blue')
    plt.plot(data2_skip_distances, label='Data2 Skip Distance', color='orange')
    plt.xlabel('Point Index')
    plt.ylabel('Distance (m)')
    plt.title(f'Skip-{SKIP} Distances within Data1 and Data2')
    plt.legend()
    plt.grid()
    # 经度方向偏差（单位：米）
    plt.subplot(3, 1, 2)
    plt.plot(data1_skip_dx, label='Data1 Delta Lon (m)', color='blue')
    plt.plot(data2_skip_dx, label='Data2 Delta Lon (m)', color='orange')
    plt.xlabel('Point Index')
    plt.ylabel('Delta Longitude (m)')
    plt.title(f'X Direction Deviation (Skip-{SKIP}, Delta Longitude)')
    plt.legend()
    plt.grid()
    # 纬度方向偏差（单位：米）
    plt.subplot(3, 1, 3)
    plt.plot(data1_skip_dy, label='Data1 Delta Lat (m)', color='blue')
    plt.plot(data2_skip_dy, label='Data2 Delta Lat (m)', color='orange')
    plt.xlabel('Point Index')
    plt.ylabel('Delta Latitude (m)')
    plt.title(f'Y Direction Deviation (Skip-{SKIP}, Delta Latitude)')
    plt.legend()
    plt.grid()
    plt.tight_layout()
    plt.show()


def Calculatingbias(file_paths1,file_paths2,start_time, end_time):
    #计算两条轨迹之间的偏差
    df1 = read_ins_trajectory(file_paths1, start_time=start_time, end_time=end_time)
    df2 = read_ins_trajectory(file_paths2, start_time=start_time, end_time=end_time)
    # 示例数据（替换为实际的 data1 和 data2 数据）
    data1_lon = df1['lon_deg']
    data1_lat = df1['lat_deg']
    
    data2_lon = df2['lon_deg']
    data2_lat = df2['lat_deg']
    
    Calculatingbias_internal(data1_lon,data1_lat,data2_lon,data2_lat)

    # 计算每一对点之间的距离
    distances = haversine(data1_lat, data1_lon, data2_lat, data2_lon)

    # 统计最大值、最小值、平均值
    max_distance = np.max(distances)
    min_distance = np.min(distances)
    mean_distance = np.mean(distances)

    # 打印统计信息
    print(f"最大距离: {max_distance:.2f} m")
    print(f"最小距离: {min_distance:.2f} m")
    print(f"平均距离: {mean_distance:.2f} m")

    # 绘制误差曲线图
    plt.figure(figsize=(12, 6))
    plt.plot(distances, label='Point-to-Point Distance Error', color='blue')
    plt.axhline(max_distance, color='red', linestyle='--', label=f'Max: {max_distance:.2f} m')
    plt.axhline(min_distance, color='green', linestyle='--', label=f'Min: {min_distance:.2f} m')
    plt.axhline(mean_distance, color='orange', linestyle='--', label=f'Mean: {mean_distance:.2f} m')
    plt.xlabel('Point Index')
    plt.ylabel('Distance Error (m)')
    plt.title('Point-to-Point Distance Error Analysis')
    plt.legend()
    plt.grid()
    plt.show()
    

def plot_trajectories(file_paths, start_time, end_time,mag_z_mapped,lon,lat,data):
    """
    绘制多个轨迹图，并显示指定时间段内的数据
    参数:
        file_paths: 包含多个CSV文件路径的列表
        start_time: 筛选开始时间
        end_time: 筛选结束时间
    """

    #Calculatingbias(file_paths[0], file_paths[1], start_time, end_time)

    #mag_z_mapped[Ymin:Ymax,Xmin:Xmax]=0

    plt.figure(figsize=(12, 10))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], origin='lower', cmap='jet')
    plt.plot(data['Lon'], data['Lat'], linewidth=4, label='Geomagnetic data')
    # 处理每个文件
    for file_path in file_paths:
        try:
            df = read_ins_trajectory(file_path, start_time=start_time, end_time=end_time)
            plt.plot(df['lon_deg'], df['lat_deg'], linewidth=1.5, label=os.path.basename(file_path))
            #print(df['time'][len(df['time'])-1])
            #plt.scatter(df['lon_deg'], df['lat_deg'], s=10, label=os.path.basename(file_path), alpha=0.6)
            # 添加起点和终点标记
            if not df.empty:
                plt.plot(df['lon_deg'].iloc[0], df['lat_deg'].iloc[0], 'go', markersize=8)  # 起点
                plt.plot(df['lon_deg'].iloc[-1], df['lat_deg'].iloc[-1], 'ro', markersize=8)  # 终点
        except Exception as e:
            print(f"文件处理失败: {file_path}, 错误: {str(e)}")
    plt.grid(True)
    plt.xlabel('Longitude (deg)')
    plt.ylabel('Latitude (deg)') 
    plt.title('INS Trajectories')
    plt.legend()

    # 保存图片到当前脚本所在目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    save_path = os.path.join(current_dir, 'ins_trajectories.png')
    #plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def plot_deff(diff_x,diff_y,diff_z):
    # 创建一个包含所有点索引的数组
    point_indices = np.arange(len(diff_x))
    # 创建图形和单个子图
    fig, ax = plt.subplots(figsize=(10, 7))
    # 绘制 X 分量差异曲线
    ax.plot(point_indices, diff_x, label='X Difference', color='red')
    # 绘制 Y 分量差异曲线
    ax.plot(point_indices, diff_y, label='Y Difference', color='green')
    # 绘制 Z 分量差异曲线
    ax.plot(point_indices, diff_z, label='Z Difference', color='blue')
    # 设置标题和标签
    ax.set_title('Differences in X, Y and Z Components')
    ax.set_xlabel('Point Index')
    ax.set_ylabel('Difference Value')
    # 添加图例
    ax.legend()
    # 添加网格线以提高可读性
    ax.grid(True)
    # 调整布局以防止重叠
    plt.tight_layout()
    # 显示图形
    plt.show()


def SegmentStatistics(ori_np,Lab_np):

    '''
    # 计算欧几里得距离
    distances = np.linalg.norm(ori_np - Lab_np, axis=1)
    # 计算平均距离、最大距离和最小距离
    mean_distance = np.mean(distances)
    max_distance = np.max(distances)
    min_distance = np.min(distances)
    print(f"Average Distance: {mean_distance}")
    print(f"Maximum Distance: {max_distance}")
    print(f"Minimum Distance: {min_distance}")
    '''
    
    diff_x = ori_np[:, 0] - Lab_np[:, 0]
    diff_y = ori_np[:, 1] - Lab_np[:, 1]
    diff_z = ori_np[:, 2] - Lab_np[:, 2]
    

    #plot_deff(diff_x,diff_y,diff_z)
    abs_diff_x = np.abs(ori_np[:, 0] - Lab_np[:, 0])
    abs_diff_y = np.abs(ori_np[:, 1] - Lab_np[:, 1])
    abs_diff_z = np.abs(ori_np[:, 2] - Lab_np[:, 2])

    
    # 定义需要检查的范围
    ranges = range(1, 11)
    
    # 初始化一个字典来存储结果
    results = {f'diff_{dim}': {i: 0 for i in ranges} for dim in ['x', 'y', 'z']}
    
    # 计算每个分量的绝对值在各个范围内的百分比
    for r in ranges:
        results['diff_x'][r] = np.mean(abs_diff_x <= r) * 100
        results['diff_y'][r] = np.mean(abs_diff_y <= r) * 100
        results['diff_z'][r] = np.mean(abs_diff_z <= r) * 100
        
    # 打印结果
    for diff, range_dict in results.items():
        print(f"Percentage of absolute values for {diff} within:")
        for r, percentage in range_dict.items():
            print(f"\t{r}: {percentage:.2f}%")
            
    # 计算每个分量的最大、最小和平均差值
    stats = {
        'X': {'max': np.max(diff_x), 'min': np.min(diff_x), 'mean': np.mean(np.abs(diff_x))},
        'Y': {'max': np.max(diff_y), 'min': np.min(diff_y), 'mean': np.mean(np.abs(diff_y))},
        'Z': {'max': np.max(diff_z), 'min': np.min(diff_z), 'mean': np.mean(np.abs(diff_z))}
    }
    
    # 打印结果
    for component, values in stats.items():
        print(f"{component} Component:")
        print(f"  Max Difference: {values['max']:.6f}")
        print(f"  Min Difference: {values['min']:.6f}")
        print(f"  Mean Difference: {values['mean']:.6f}")



    


def visualize_sample(data_dir, image_size=224):
    """
    随机读取一个生成的数据并可视化显示真实轨迹、噪声轨迹以及候选点的位置
    """
    # 获取所有生成的数据文件
    files = [f for f in os.listdir(data_dir) if f.endswith('.pth')]
    if not files:
        print("No data files found in the directory!")
        return

    # 随机选取一个文件
    selected_file = random.choice(files)
    print(f"Randomly selected file: {selected_file}")

    # 加载数据
    data_path = os.path.join(data_dir, selected_file)
    data = torch.load(data_path)

    # 提取数据
    images = data['images'].numpy()  # Shape: (8, 3, image_size, image_size)
    noisy_trajectory = data['trajectory'].numpy().reshape(-1, 2)  # Shape: (10, 2)
    noise = data['target'].numpy().reshape(-1, 2)  # Shape: (10, 2)
    real_trajectory = noisy_trajectory - noise

    # 可视化真实轨迹、噪声轨迹及候选点
    plt.figure(figsize=(12, 10))
    # 绘制真实轨迹
    plt.plot(real_trajectory[:, 0], real_trajectory[:, 1], 'bo-', label='Real Trajectory')
    # 绘制噪声轨迹
    plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 'go-', label='Noisy Trajectory')

    # 遍历绘制候选点
    for i in range(len(images)):
        candidate_points = np.argwhere(images[i] == 1)  # 提取候选点的坐标
        plt.scatter(candidate_points[:,0], candidate_points[:,1], s=10, label=f"Candidates (Point {i+1})")

    # 设置标题和图例
    plt.title("Visualization of Trajectories and Candidate Points", fontsize=16)
    plt.xlabel("X Position", fontsize=12)
    plt.ylabel("Y Position", fontsize=12)
    plt.legend(loc='best', fontsize=10)
    plt.grid(True)
    plt.savefig("trajectory_plot.png", dpi=300) 
    plt.show()


def savedata(X_LAB,Y_LAB,Z_LAB,trajectory_error,trajectory_mag,trajectory_true,num,data_dir,i,best_region):
    
    #X_LAB,Y_LAB,Z_LAB [行(y)，列（x）]
    #data_dir="my_train_data"
    image_size=224
    tolerance_x=1
    tolerance_y=2
    tolerance_z=1
    images = []
    image = np.zeros((image_size, image_size), dtype=np.uint8)
    for point in trajectory_mag:
        mag_x_val, mag_y_val, mag_z_val = point
        image.fill(0)
        # 利用 NumPy 的广播特性，计算 X_LAB, Y_LAB 和 Z_LAB 的差值
        diff_x = np.abs(X_LAB - mag_x_val)
        diff_y = np.abs(Y_LAB - mag_y_val)
        diff_z = np.abs(Z_LAB - mag_z_val)
        # 查找满足条件的坐标并更新图像
        if 0: #trajectory_true[0][0]+550==724:
            #print("ooooooooooooooo",trajectory_true[0][0])
            mask = (diff_y <= 4) & (diff_z-7 <= tolerance_z)
        else:
            #print("rrrrrrrrrrrrrrrr",trajectory_true[0][0])
            mask = (diff_x <= tolerance_x) & (diff_y <= tolerance_y) & (diff_z <= tolerance_z)
        mask_T = mask.T
        image[mask_T] = 1
        # 将处理好的图像加入到图像序列中
        images.append(image.copy())
    image_sequence = np.array(images)


    # 计算轨迹的 x 和 y 范围
    image_size=224-100
    min_x, min_y = np.array(trajectory_error).min(axis=0)
    max_x, max_y = np.array(trajectory_error).max(axis=0)
    # 计算平移量，确保所有点在 [0, image_size-1] 范围内
    translation_x = 0
    translation_y = 0
    if min_x < 0:
        translation_x = -min_x  # 如果最小 x 超出了左边界，则平移至 0
    elif max_x >= image_size:
        translation_x = image_size - max_x  # 如果最大 x 超出了右边界，则平移至图像右边界
    if min_y < 0:
        translation_y = -min_y  # 如果最小 y 超出了上边界，则平移至 0
    elif max_y >= image_size:
        translation_y = image_size - max_y  # 如果最大 y 超出了下边界，则平移至图像下边界
    # 进行平移操作
    move=np.array([translation_x, translation_y])
    trajectory_error += move
    
    noise=np.array(trajectory_error)-np.array(trajectory_true)

    NUM_ID=np.array([num,i])
    region=np.array(best_region)
    data_dict = {
        'images': torch.tensor(image_sequence, dtype=torch.float32),  # Shape: (8, 3, 224, 224)
        'trajectory': torch.tensor(trajectory_error.flatten(), dtype=torch.float32),  # Shape: (16,)
        'target': torch.tensor(noise.flatten(), dtype=torch.float32),  # Shape: (16,)
        'move': torch.tensor(move, dtype=torch.float32),
        'id': torch.tensor(NUM_ID, dtype=torch.float32),
        'best_region': torch.tensor(region.flatten(), dtype=torch.float32)      
    }

    num=NUM+num
    file_path = os.path.join(data_dir, f'tr_{num}.pth')
    torch.save(data_dict, file_path)
    print(f"Sample {num} saved.")
    return 1


def FindTrajectory(start_idx,pos_wd,len_points,step,region):
    trajectory = [pos_wd[start_idx][:2]]  # 初始化轨迹，只考虑 x 和 y
    current_idx = start_idx
    current_point = pos_wd[start_idx][:2]  # 只取 x 和 y
    '''
    x_max=Xmax
    x_min=Xmin
    y_max=Ymax
    y_min=Ymin

    '''
    x_max=region[3]
    x_min=region[2]
    y_max=region[1]
    y_min=region[0]
    
    # 首先检查起始点是否在范围内
    start_point = trajectory[0]
    if not (x_min <= start_point[0] < x_max and y_min <= start_point[1] < y_max):
        return [], False  # 如果起始点不在范围内，直接返回空列表和标志量 False

    # 循环找到 len_points 个轨迹点
    for _ in range(1, len_points):
        for i in range(current_idx + 1, len(pos_wd)):
            # 计算当前位置与候选点的距离，只考虑 x 和 y
            next_point = pos_wd[i][:2]
            dist = np.linalg.norm(np.array(next_point) - np.array(current_point))
            
            if dist >= step:
                # 如果距离满足要求，选择该点并更新当前位置
                trajectory.append(next_point)
                current_point = next_point
                current_idx = i
                break
    # 检查轨迹的终点是否在范围内
    #end_point = trajectory[-1]
    #if not (x_min <= end_point[0] < x_max and y_min <= end_point[1] < y_max):
    #    print(end_point)
    #    return trajectory, False  # 如果终点不在范围内，返回标志量 False

    for point in trajectory:
        if not (x_min <= point[0] < x_max and y_min <= point[1] < y_max):
            #print(f"Point outside bounds: {point}")
            return trajectory, False

    if len(trajectory)<len_points:
        return trajectory, False

    #print("trajectory: ",trajectory)

    trajectory = [[point[0] - x_min, point[1] - y_min] for point in trajectory]

    #print("region", region)
    #print("iii",trajectory)

    return trajectory, True
    
def generate_mag_and_error_trajectory(trajectory_true, Xmap, Ymap, Zmap, MAP_GD_X, MAP_GD_Y,map_type,region):
    trajectory_mag = []
    trajectory_error=[]
    
    '''
    x_max=Xmax
    x_min=Xmin
    y_max=Ymax
    y_min=Ymin
    '''

    x_max=region[3]
    x_min=region[2]
    y_max=region[1]
    y_min=region[0]
    
    
    #print(trajectory_true)
    for point in trajectory_true:
        x, y = point
        
        # 检查 x, y 是否在合法范围内
        if 0 <= x < Xmap.shape[0] and 0 <= y < Ymap.shape[1]:
            xtemp=x+x_min
            ytemp=y+y_min

            if map_type==0:  #生成轨迹上的测量测图
                X_val = Xmap[xtemp, ytemp]
                Y_val = Ymap[xtemp, ytemp]
                Z_val = Zmap[xtemp, ytemp]

            #====================================
            if map_type==1: #生成轨迹上的测量测图
                X_val = Xmap[ytemp,xtemp]
                Y_val = Ymap[ytemp,xtemp]
                Z_val = Zmap[ytemp,xtemp]
            
            # 添加对应的 Xmap, Ymap, Zmap 的值
            trajectory_mag.append([X_val, Y_val, Z_val])
            trajectory_error.append([MAP_GD_X[xtemp, ytemp],MAP_GD_Y[xtemp, ytemp]])
            #print(x, y,MAP_GD_X[xtemp, ytemp],MAP_GD_Y[xtemp, ytemp])
        else:
            raise IndexError(f"坐标 ({x}, {y}) 超出有效范围，无法查找对应的磁场值！")

    trajectory_error = [[point[0] - x_min, point[1] - y_min] for point in trajectory_error]
    #print(trajectory_true)
    #print(trajectory_error)
    return trajectory_mag,trajectory_error
#=============================================6#=================================================
regions_6 = [
    #[550, 774, 550, 774], # 区域1
    [550, 774, 550, 774], # 区域1
    [500, 724, 550, 774], # 区域2
    [400, 624, 550, 774], # 区域3
    [300, 524, 550, 774], # 区域4
    [200, 424, 550, 774], # 区域5
    [100, 324, 550, 774], # 区域6
    [0, 224, 550, 774],   # 区域7
    [550, 774, 430, 654], # 区域8
    [500, 724, 430, 654], # 区域9
    [400, 624, 430, 654], # 区域10
    [300, 524, 430, 654], # 区域11
    [200, 424, 430, 654], # 区域12
    [100, 324, 430, 654], # 区域13
    [0, 224, 430, 654]    # 区域14
]

#=============================================7#=================================================
regions_7 = [
    [550, 774, 256,480], # 区域1
    [500, 724, 256,480], # 区域2
    [400, 624, 256,480], # 区域3
    [300, 524, 256,480], # 区域4
    [200, 424, 256,480], # 区域5
    [100, 324, 256,480], # 区域6
    [0, 224, 256,480]    # 区域7
]
#=============================================8#=================================================
regions_8 = [
    #[550, 774, 550, 774], # 区域1
    [550, 774, 126,350], # 区域1
    [500, 724, 126,350], # 区域2
    [400, 624, 126,350], # 区域3
    [300, 524, 126,350], # 区域4
    [200, 424, 126,350], # 区域5
    [100, 324, 126,350], # 区域6
    [0, 224, 126,350]
]

#=============================================9#=================================================
regions_9 = [
    #[550, 774, 550, 774], # 区域1
    [550, 774, 0, 224], # 区域1
    [500, 724, 0, 224], # 区域2
    [400, 624, 0, 224], # 区域3
    [300, 524, 0, 224], # 区域4
    [200, 424, 0, 224], # 区域5
    [100, 324, 0, 224], # 区域6
    [0, 224, 0, 224],   # 区域7
]

regions=regions_9

def point_in_region(x, y, region):
    """判断点是否在矩形区域内"""
    y_min, y_max, x_min, x_max = region
    return x_min <= x < x_max and y_min <= y < y_max

def distance_to_boundary(x, y, region):
    """计算点到矩形边界最近的距离"""
    y_min, y_max, x_min, x_max = region
    #left = x - x_min
    #right = x_max - x
    top = y - y_min
    bottom = y_max - y
    return min(top, bottom)

def find_farthest_region(x, y, regions):
    """找到包含点且距离边界最远的区域"""
    farthest_distance = -np.inf
    best_region = None
    print(x, y)
    for i, region in enumerate(regions):
        #print(region)
        if point_in_region(x, y, region):
            dist = distance_to_boundary(x, y, region)
            if dist > farthest_distance:
                farthest_distance = dist
                best_region = region  # 区域编号从1开始
    return best_region

def DataGeneration(file_path_GD,file_path_WD, start_time, end_time,lon,lat,X_LAB,Y_LAB,Z_LAB,data_CL,mag_z_mapped,data_dir):

    #============================================================================================
    #file_path:  架次实测
    #lon,lat,X_LAB,Y_LAB,Z_LAB： 来自map的数据
    #data_CL： 实时飞行数据
    #============================================================================================
    print(mag_z_mapped.shape)
    print(type(mag_z_mapped))
    df_GD = read_ins_trajectory(file_path_GD, start_time=start_time, end_time=end_time)#惯导
    df_WD = read_ins_trajectory(file_path_WD, start_time=start_time, end_time=end_time)#卫导
    print(len(df_GD),len(df_WD))
    
    #mag_z_mapped[Ymin:Ymax,Xmin:Xmax]=0

    '''
    mag_z_mapped[550:774,550:774]=0
    mag_z_mapped[500:724,550:774]=0
    mag_z_mapped[400:624,550:774]=0
    mag_z_mapped[300:524,550:774]=0
    mag_z_mapped[200:424,550:774]=0
    mag_z_mapped[100:324,550:774]=0
    mag_z_mapped[0:224,550:774]=0

    mag_z_mapped[550:774,430:654]=0.5
    mag_z_mapped[500:724,430:654]=0.5
    mag_z_mapped[400:624,430:654]=0.5
    mag_z_mapped[300:524,430:654]=0.5
    mag_z_mapped[200:424,430:654]=0.5
    mag_z_mapped[100:324,430:654]=0.5
    mag_z_mapped[0:224,430:654]=0.5
    '''
    
    MAP_WD_LON=np.zeros((840, 840), dtype=np.float32) 
    MAP_WD_Lat=np.zeros((840, 840), dtype=np.float32) 

    MAP_GD_X=np.zeros((840, 840), dtype=np.int32)
    MAP_GD_Y=np.zeros((840, 840), dtype=np.int32)

    X_MAP_CL=np.zeros((840, 840), dtype=np.float32)   # 实测的磁场数据
    Y_MAP_CL=np.zeros((840, 840), dtype=np.float32) 
    Z_MAP_CL=np.zeros((840, 840), dtype=np.float32) 
    #===========================================================
    for i in range(len(data_CL['Lon'])):
        lon_=data_CL['Lon'][i]
        lat_=data_CL['Lat'][i]
        x_=(lon_-lon[0,0])/(lon[839,839]-lon[0,0])*840.0
        y_=(lat_-lat[0,0])/(lat[839,839]-lat[0,0])*840.0

        x_idx = int(round(x_))# mag_z_mapped2 检索需要
        y_idx = int(round(y_))

        X_MAP_CL[x_idx,y_idx] = data_CL['X'][i]  # 实测的磁场数据
        Y_MAP_CL[x_idx,y_idx] = data_CL['Y'][i]
        Z_MAP_CL[x_idx,y_idx] = data_CL['Z'][i]

    #===========================================================
    pos_wd = []
    last_x = 0
    last_y = 0
    
    for i in range(0, len(df_WD['lon_deg']), 10):
        lon_=df_WD['lon_deg'].iloc[i]
        lat_=df_WD['lat_deg'].iloc[i]
        x_=(lon_-lon[0,0])/(lon[839,839]-lon[0,0])*840.0
        y_=(lat_-lat[0,0])/(lat[839,839]-lat[0,0])*840.0
        
        y_idx = int(round(x_))# mag_z_mapped2 检索需要
        x_idx = int(round(y_))
 
        if i==0:
            pos_wd.append([int(round(x_)),int(round(y_)),i])
            last_x=int(round(x_))
            last_y=int(round(y_))
        elif not(y_idx==last_x and x_idx==last_y):
            pos_wd.append([int(round(x_)),int(round(y_)),i]) # 卫导的实际位置
            last_x=int(round(x_))
            last_y=int(round(y_))
            
        if x_idx>=0 and x_idx<840 and y_idx>=0 and y_idx<840:
            lon_=df_GD['lon_deg'].iloc[i]
            lat_=df_GD['lat_deg'].iloc[i]
            x_t=(lon_-lon[0,0])/(lon[839,839]-lon[0,0])*840.0
            y_t=(lat_-lat[0,0])/(lat[839,839]-lat[0,0])*840.0
                
            MAP_WD_LON[y_idx,x_idx]=df_WD['lon_deg'].iloc[i]
            MAP_WD_Lat[y_idx,x_idx]=df_WD['lat_deg'].iloc[i]
            
            MAP_GD_X[y_idx,x_idx]=int(round(x_t))  ##卫导的实际位置对应的带误差的惯导位置
            MAP_GD_Y[y_idx,x_idx]=int(round(y_t))
            
            #print(y_idx,x_idx,x_,y_)
            #if  x_idx>320 and x_idx<504 and y_idx>520 and y_idx<704:
            #    mag_z_mapped[x_idx-5:x_idx+5,y_idx-5:y_idx+5] = 0.8

    print("pos_wd:  ",  len(pos_wd))
    non_zero_count = np.count_nonzero(MAP_GD_X)
    print(f"MAP_GD_X 非零元素数量: {non_zero_count}")
    #return
    #print(len(pos_wd),pos_wd[0:10])

    num=0
    
    with open('output_data.txt', 'a') as file:
      for i in range(len(pos_wd)):
          x=pos_wd[i][0]  #列
          y=pos_wd[i][1]
          best_region=find_farthest_region(x,y,regions)
          if best_region  is  None:
              continue
          #print("eeeeeeee",best_region)
  
          Xmax=best_region[3]
          Xmin=best_region[2]
          Ymax=best_region[1]
          Ymin=best_region[0]
  
          #if num>2000:
          #    break
  
          if  y>=Ymin and y<=Ymax and x>=Xmin and x <= Xmax:  #在所选区间内
                  mag_z_mapped[y-5:y+5,x-5:x+5] = 0.8
                  trajectory_true,ValidMark=FindTrajectory(i,pos_wd,10,6,best_region) #往后选择十个点
                  #print(ValidMark)
                  if ValidMark:
                      trajectory_mag,trajectory_error=generate_mag_and_error_trajectory(trajectory_true,X_MAP_CL,Y_MAP_CL,Z_MAP_CL,MAP_GD_X,MAP_GD_Y,0,best_region)
                      #print(trajectory_mag)
                      #trajectory_mag,trajectory_error=generate_mag_and_error_trajectory(trajectory_true,X_LAB,Y_LAB,Z_LAB,MAP_GD_X,MAP_GD_Y,1)
                      savedata(X_LAB[Ymin:Ymax,Xmin:Xmax],Y_LAB[Ymin:Ymax,Xmin:Xmax],Z_LAB[Ymin:Ymax,Xmin:Xmax],trajectory_error,trajectory_mag,trajectory_true,num,data_dir,i,best_region)
                      file.write(f"{num},{Xmax},{Xmin},{Ymax},{Ymin}\n") #best_region.txt
                      #print(num)
                      num = num + 1
    
    '''
    plt.figure(figsize=(12, 10))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], origin='lower', cmap='jet')
    plt.plot(df_WD['lon_deg'], df_WD['lat_deg'], linewidth=1.5, label="df_WD")
    plt.plot(df_GD['lon_deg'], df_GD['lat_deg'], linewidth=1.5, label="df_GD")
    
    plt.plot(data_CL['Lon'], data_CL['Lat'], linewidth=4, label='Geomagnetic data')
    plt.legend()
    plt.show()
    '''





def main():
    """
    主函数
    """
    data_dir = r"data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/train"
    while(0):
        visualize_sample(data_dir)
    
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    #磁图数据
    rgb_matrix,lon,lat,X_LAB,Y_LAB,Z_LAB=load_and_plot_magnetic_field(file_path)

    #file_path2 = r'altitude_3000_3D_out_zrc1107_rm_direction_diff_aircraft_number_6.csv'
    data=read_XYZ_trajectory(file_path2)

    '''
    #=============================================增加对测量值的滤波=======================================================
    def moving_average_history_cumsum(data, window_size=3):
        """严格基于历史值的移动平均滤波"""
        cumsum_vec = np.cumsum(np.insert(data, 0, 0))  # 计算累积和
        return (cumsum_vec[window_size:] - cumsum_vec[:-window_size]) / window_size
    
        # 对 X, Y, Z 数据进行滤波
    window_size = 10  # 设置窗口大小
    
    #对称滤波
    #start_index = (window_size - 1) // 2
    #filtered_X = moving_average_filter(data['X'], window_size)
    #filtered_Y = moving_average_filter(data['Y'], window_size)
    #filtered_Z = moving_average_filter(data['Z'], window_size)
    
    #历史滤波
    start_index = window_size - 1
    filtered_X = moving_average_history_cumsum(data['X'], window_size)
    filtered_Y = moving_average_history_cumsum(data['Y'], window_size)
    filtered_Z = moving_average_history_cumsum(data['Z'], window_size)
    # 计算可以匹配的起始索引
    data.loc[start_index:(len(filtered_X)+start_index-1), 'X'] = filtered_X
    data.loc[start_index:(len(filtered_X)+start_index-1), 'Y'] = filtered_Y
    data.loc[start_index:(len(filtered_X)+start_index-1), 'Z'] = filtered_Z
    #====================================================================================================================
    '''
    
    '''
    plt.figure(figsize=(8, 6))
    contour = plt.contour(Z_LAB, levels=20, cmap='viridis')
    levels = np.linspace(Z_LAB.min(), Z_LAB.max(), 10)  # 根据需要调整等分的数量
    plt.contour(Z_LAB, levels=levels, cmap='viridis')
    #plt.colorbar(contour)
    plt.title('Contour Plot of Z_LAB')
    plt.xlabel('X-axis')
    plt.ylabel('Y-axis')
    plt.show()
    '''

    '''
    ori=[]
    Lab=[]
    for i in range(len(data['Lon'])):
        lon_=data['Lon'][i]
        lat_=data['Lat'][i]
        x_=(lon_-lon[0,0])/(lon[839,839]-lon[0,0])*840.0
        y_=(lat_-lat[0,0])/(lat[839,839]-lat[0,0])*840.0
        y_idx = int(round(x_))
        x_idx = int(round(y_))

        # 添加原始坐标到 ori
        ori.append([data['X'][i], data['Y'][i], data['Z'][i]])  #实际飞行时 实测的数据
        # 添加转换后的坐标到 Lab
        Lab.append([X_LAB[x_idx, y_idx], Y_LAB[x_idx, y_idx], Z_LAB[x_idx, y_idx]])  # 实际飞行时 对应的真值数据
        #if i==0:
        #    print(lon_,lat_,ori[i],x_,y_)
        #    print(lon[x_idx, y_idx],lat[x_idx, y_idx],Lab[i],x_idx, y_idx)

    # 将列表转换为 NumPy 数组以便于计算
    ori_np = np.array(ori)
    Lab_np = np.array(Lab)
    #SegmentStatistics(ori_np,Lab_np) #分段统计
    '''
    
    if not file_paths:
        print("未提供文件路径")
        return

    #====================================================================================================================
    #file_paths[0],file_paths[1]：  INS数据与惯卫融合数据
    #start_time, end_time：         截取相同时间段的数据
    #lon,lat： 磁图数据对应的经纬度
    #X_LAB,Y_LAB,Z_LAB： 磁图数据栅格化的三分量数据
    #data：  实际飞行时 实测的磁场数据
    #rgb_matrix：背景图像
    #data_dir：保存的数据
    #====================================================================================================================

    DataGeneration(file_paths[0],file_paths[1], start_time, end_time,lon,lat,X_LAB,Y_LAB,Z_LAB,data,rgb_matrix,data_dir)
    
    # 绘制轨迹
    #plot_trajectories(file_paths, start_time, end_time,rgb_matrix,lon,lat,data)
    #except Exception as e:
    #    print(f"处理数据时出错: {str(e)}")

    visualize_sample(data_dir)

if __name__ == "__main__":
    main()









