import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
import random
import matplotlib.pyplot as plt
from scipy.spatial.transform import Rotation as R
from transformers import ViTForImageClassification
from transformers import ViTModel, ViTConfig
import matplotlib.pyplot as plt
from torch.utils.data import DataLoader,Subset, Dataset
import os
import torch.nn.functional as F
from transformers import ViTConfig
from torchvision  import transforms
import time
import shutil
import datetime
import json
import re

# 全局配置参数
current_time = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S")  # 当前时间戳，用于保存结果
BATCHSIZE_ = 10                     # 训练批次大小
BATCHSIZE_Test = 10                 # 测试批次大小
ALLNUM_ = 900                       # 每epoch使用的样本数
ALLNUM_FOR_TRAIN = 3422            # 训练集总样本数
Train_state = 0                     # 训练状态标志(0:测试,1:训练)
a = 1.5                             # 基础损失权重
b = 1.5                             # 形状损失权重

# 自动选择设备(GPU/CPU)
device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
if Train_state == 0:
    device = torch.device("cuda:0" if torch.cuda.is_available() else "cpu")
print(device)

def clear_save_path(save_path):
    """
    清理并重新创建保存路径
    参数:
        save_path: 要清理的目录路径
    """
    if os.path.exists(save_path):
        shutil.rmtree(save_path)
    os.makedirs(save_path)

class ResidualBlock(nn.Module):
    """
    残差块实现
    包含两个卷积层和快捷连接(shortcut connection)
    """
    def __init__(self, in_channels, out_channels, stride=1):
        super(ResidualBlock, self).__init__()
        # 主路径
        self.conv1 = nn.Conv2d(in_channels, out_channels, kernel_size=3, stride=stride, padding=1)
        self.bn1 = nn.BatchNorm2d(out_channels)
        self.relu = nn.ReLU()
        self.conv2 = nn.Conv2d(out_channels, out_channels, kernel_size=3, stride=1, padding=1)
        self.bn2 = nn.BatchNorm2d(out_channels)
        
        # 快捷连接
        self.shortcut = nn.Sequential()
        if stride != 1 or in_channels != out_channels:
            self.shortcut = nn.Sequential(
                nn.Conv2d(in_channels, out_channels, kernel_size=1, stride=stride, padding=0),
                nn.BatchNorm2d(out_channels)
            )
    
    def forward(self, x):
        out = self.relu(self.bn1(self.conv1(x)))
        out = self.bn2(self.conv2(out))
        out += self.shortcut(x)  # 添加快捷连接
        out = self.relu(out)
        return out

class DBResNet(nn.Module):
    """
    基础深度轨迹预测网络
    使用ViT处理图像序列，全连接层处理轨迹
    """
    def __init__(self, num_points=8):
        super(DBResNet, self).__init__()
        # ViT模型(预训练)
        self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224-in21k')
        # 轨迹处理层
        self.fc_trajectory = nn.Linear(num_points * 2, 512)
        # 特征融合层
        self.fc_fusion = nn.Linear(768 + 512, 1024)
        # 输出层
        self.fc_output = nn.Linear(1024, num_points * 2)

    def forward(self, images, trajectory):
        batch_size, seq_len, c, h, w = images.shape
        vit_features = []
        # 处理每帧图像
        for t in range(seq_len):
            vit_output = self.vit(images[:, t, :, :, :])
            vit_features.append(vit_output.last_hidden_state.mean(dim=1))
        vit_features = torch.stack(vit_features, dim=1)
        # 处理轨迹
        trajectory_features = torch.relu(self.fc_trajectory(trajectory))
        # 特征融合
        vit_features = vit_features.mean(dim=1)
        combined_features = torch.cat([vit_features, trajectory_features], dim=1)
        fusion_features = torch.relu(self.fc_fusion(combined_features))
        # 输出预测
        output = self.fc_output(fusion_features)
        output = output.view(batch_size, 10, 2)
        return output

class DBResNet4(nn.Module):
    """
    增强版深度轨迹预测网络
    结合ViT和ResNet处理图像，双路径融合特征
    """
    def __init__(self, num_points=8):
        super(DBResNet4, self).__init__()
        # ViT处理RGB图像
        self.vit = ViTModel.from_pretrained('google/vit-base-patch16-224-in21k')
        # ResNet处理灰度图像
        self.resnet = nn.Sequential(
            ResidualBlock(1, 16),
            nn.MaxPool2d(kernel_size=2, stride=2),
            ResidualBlock(16, 32),
            nn.AdaptiveAvgPool2d((1, 1)),
            nn.Flatten(),
            nn.Linear(32, 512),
            nn.ReLU()
        )
        # 轨迹处理
        self.fc_trajectory = nn.Linear(num_points * 2, 512)
        # 双路径融合
        self.fc_fusion1 = nn.Linear(768 + 512, 1024)
        self.fc_output1 = nn.Linear(1024, num_points * 2)
        self.fc_fusion2 = nn.Linear(768 + 512 + 512, 1024)
        self.fc_output2 = nn.Linear(1024, num_points * 2)

    def forward(self, images, trajectory):
        batch_size, seq_len, c, h, w = images.shape
        # ViT路径
        vit_features = []
        for t in range(seq_len):
            vit_output = self.vit(images[:, t, :, :, :])
            vit_features.append(vit_output.last_hidden_state.mean(dim=1))
        vit_features = torch.stack(vit_features, dim=1)
        # ResNet路径
        cnn_features = []
        original_image = images[:, :, 0:1, :, :]
        for t in range(seq_len):
            cnn_output = self.resnet(original_image[:, t, :, :, :])
            cnn_features.append(cnn_output)
        cnn_features = torch.stack(cnn_features, dim=1)
        # 轨迹处理
        trajectory_features = torch.relu(self.fc_trajectory(trajectory))
        # 双路径融合
        vit_features = vit_features.mean(dim=1)
        cnn_features = cnn_features.mean(dim=1)
        # 路径1: ViT + 轨迹
        combined_features1 = torch.cat([vit_features, trajectory_features], dim=1)
        fusion_features1 = torch.relu(self.fc_fusion1(combined_features1))
        output1 = self.fc_output1(fusion_features1)
        # 路径2: ViT + ResNet + 轨迹
        combined_features2 = torch.cat([vit_features, cnn_features, trajectory_features], dim=1)
        fusion_features2 = torch.relu(self.fc_fusion2(combined_features2))
        output2 = self.fc_output2(fusion_features2)
        # 合并输出
        output = output1.view(batch_size, 10, 2) + output2.view(batch_size, 10, 2)
        return output

class TrajectoryLoss(nn.Module):
    """
    轨迹损失函数
    包含两部分:
    1. 间距一致性损失(相邻点距离应相似)
    2. 直线形状损失(相邻点斜率应一致)
    """
    def __init__(self, alpha=1.0, beta=1.0):
        super(TrajectoryLoss, self).__init__()
        self.alpha = alpha  # 间距一致性权重
        self.beta = beta    # 直线形状权重

    def forward(self, outputs):
        # 计算相邻点距离
        diffs = outputs[:, 1:, :] - outputs[:, :-1, :]
        distances = torch.norm(diffs, p=2, dim=2)
        # 间距一致性损失(距离方差)
        mean_distances = torch.mean(distances, dim=1, keepdim=True)
        variance = torch.mean((distances - mean_distances) ** 2, dim=1)
        distance_loss = torch.mean(variance)
        # 直线形状损失(斜率变化)
        gradients = diffs[:, 1:, :] - diffs[:, :-1, :]
        gradient_loss = torch.mean(torch.norm(gradients, p=2, dim=2) ** 2)
        # 总损失
        return self.alpha * distance_loss + self.beta * gradient_loss

def eva(model, eva_loader, save_path="test_images3/", IsSHOW=0):
    model.eval()
    with torch.no_grad():
        running_loss = 0.0
        euclidean_distance_all = 0.0

        # 从save_path推导root_path（向上两级目录）
        root_path = os.path.dirname(os.path.dirname(save_path))
        # 结果文件保存到results目录，使用时间戳命名
        results_dir = os.path.join(root_path, "results")
        result_file = os.path.join(results_dir, f"evaluation_results_{os.path.basename(save_path)}.txt")
        # 确保results目录存在
        os.makedirs(results_dir, exist_ok=True)
        
        with open(result_file, 'a') as result_f:
            for i, (images, trajectories, targets, path) in enumerate(eva_loader):
                match = re.search(r'([^/]+)\.pth$', path[0])
                if match:
                    file_name = match.group(1)  
                #print("oooooo",path,file_name)

                images, trajectories, targets = images.to(device), trajectories.to(device), targets.to(device)
                start_time = time.time()  # 记录开始时间
                outputs = model(images, trajectories)
                end_time = time.time()  # 记录结束时间
                # 计算推理时间
                inference_time = end_time - start_time
                #print(f'Inference time: {inference_time:.6f} seconds')
                # 计算间距一致性损失和直线形状损失
                traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)  # 可以根据需要调整 alpha 和 beta
                shape_loss = traj_loss(trajectories.view(-1, 10, 2)-outputs)
                euclidean_distance = torch.norm(outputs.view(-1, 10, 2)  - targets.view(-1, 10, 2) , p=2, dim=2)
                euclidean_distance_all=euclidean_distance_all+torch.mean(torch.mean(euclidean_distance, dim=1).cpu())
                
                diff = (trajectories.view(-1, 10, 2) - outputs).cpu().numpy()
                result_f.write(f"{file_name} {diff[0][0,0]} {diff[0][0,1]} {diff[0][1,0]} {diff[0][1,1]} {diff[0][2,0]} {diff[0][2,1]} {diff[0][3,0]} {diff[0][3,1]} {diff[0][4,0]} {diff[0][4,1]} {diff[0][5,0]} {diff[0][5,1]} {diff[0][6,0]} {diff[0][6,1]} {diff[0][7,0]} {diff[0][7,1]} {diff[0][8,0]} {diff[0][8,1]} {diff[0][9,0]} {diff[0][9,1]}\n")
                #print(diff)

                if IsSHOW:
                    # 保存测试图像和参考轨迹
                    for j in range(images.shape[0]):
                        plt.figure(figsize=(8, 6))
                        true_trajectory = targets[j].cpu().numpy().reshape(-1, 2)
                        noisy_trajectory = trajectories[j].cpu().numpy().reshape(-1, 2)  # 获取噪声轨迹
                        predicted_trajectory = outputs[j].cpu().numpy()
                        #print(true_trajectory)
                        #print(predicted_trajectory)
                        # 绘制真实轨迹、噪声轨迹和输出轨迹
                        plt.plot(noisy_trajectory[:, 0]-true_trajectory[:, 0], noisy_trajectory[:, 1]-true_trajectory[:, 1], 'bo-', label="True Trajectory")
                        plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 'go-', label="Noisy Trajectory")  # 绘制噪声轨迹
                        plt.plot(noisy_trajectory[:, 0]-predicted_trajectory[:, 0], noisy_trajectory[:, 1]-predicted_trajectory[:, 1], 'ro-', label="Predicted Trajectory")
                        
                        plt.title ("True vs Predicted vs Noisy Trajectory")
                        plt.xlabel("X Position")
                        plt.ylabel("Y Position")
                        plt.legend()

                        # 保存参考图像和轨迹图
                        image_filename = os.path.join(save_path, f"_trajectory_{i*images.shape[0] + j}.png")
                        plt.savefig(image_filename)
                        plt.close()
                    # 保存图像
                    for j in range(images.shape[0]):
                        # 获取单张图像并转换为HWC格式
                        image_j = images[j].cpu().numpy()
                        for o in range(image_j.shape[0]):    
                            image_filename = os.path.join(save_path, f"_image_{i*images.shape[0] + j}_{o}.png")
                            #print(f"Image shape: {image_j.shape}")  # 检查图像形状
                            image_o=image_j[o].transpose(1, 2, 0)  # 转换为 HWC 格式
                            plt.imshow(image_o)
                            plt.axis('off')
                            plt.savefig(image_filename)
                            plt.close()
            print("all:  ",euclidean_distance_all/ len(eva_loader))
        return euclidean_distance_all/ len(eva_loader)






def test(model, test_loader, save_path="test_images3/", IsSHOW=0):
    """
    测试模型性能
    参数:
        model: 要测试的模型
        test_loader: 测试数据加载器
        save_path: 结果保存路径
        IsSHOW: 是否可视化结果
    返回:
        平均测试损失
    """
    model.eval()
    with torch.no_grad():
        running_loss = 0.0
        euclidean_distance_all = 0
        euclidean_distance_all_w = 0

        for i, (images, trajectories, targets, _) in enumerate(test_loader):
            images, trajectories, targets = images.to(device), trajectories.to(device), targets.to(device)
            # 推理
            start_time = time.time()
            outputs = model(images, trajectories)
            end_time = time.time()
            inference_time = end_time - start_time
            print(f'Inference time: {inference_time:.6f} seconds')
            # 计算损失
            traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)
            shape_loss = traj_loss(trajectories.view(-1, 10, 2) - outputs)
            # 计算欧氏距离
            outputs_reshaped = outputs.view(-1, 10, 2)
            targets_reshaped = targets.view(-1, 10, 2)
            euclidean_distance = torch.norm(outputs_reshaped - targets_reshaped, p=2, dim=2)
            euclidean_distance_all += torch.mean(torch.mean(euclidean_distance, dim=1).cpu())
            # 加权欧氏距离
            diff = outputs_reshaped - targets_reshaped
            diff_weighted = torch.zeros_like(diff)
            diff_weighted[..., 0] = diff[..., 0] * 100.0      # x方向权重
            diff_weighted[..., 1] = diff[..., 1] * 132.17     # y方向权重
            euclidean_distance_w = torch.norm(diff_weighted, p=2, dim=2)
            euclidean_distance_all_w += torch.mean(torch.mean(euclidean_distance_w, dim=1).cpu())
            # 基础损失
            outputs2 = outputs.view(-1, 20)
            targets = targets.view(-1, 20)
            base_loss = criterion(outputs2, targets)
            loss = a * base_loss + b * shape_loss
            running_loss += loss.item()
            # 可视化
            if IsSHOW:
                # 保存轨迹对比图
                for j in range(images.shape[0]):
                    plt.figure(figsize=(8, 6))
                    true_trajectory = targets[j].cpu().numpy().reshape(-1, 2)
                    noisy_trajectory = trajectories[j].cpu().numpy().reshape(-1, 2)
                    predicted_trajectory = outputs[j].cpu().numpy()
                    plt.plot(noisy_trajectory[:, 0]-true_trajectory[:, 0], 
                            noisy_trajectory[:, 1]-true_trajectory[:, 1], 
                            'bo-', label="True Trajectory")
                    plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 
                            'go-', label="Noisy Trajectory")
                    plt.plot(noisy_trajectory[:, 0]-predicted_trajectory[:, 0], 
                            noisy_trajectory[:, 1]-predicted_trajectory[:, 1], 
                            'ro-', label="Predicted Trajectory")
                    plt.title("True vs Predicted vs Noisy Trajectory")
                    plt.xlabel("X Position")
                    plt.ylabel("Y Position")
                    plt.legend()
                    image_filename = os.path.join(save_path, f"_trajectory_{i*images.shape[0] + j}.png")
                    plt.savefig(image_filename)
                    plt.close()
                # 保存输入图像
                for j in range(images.shape[0]):
                    image_j = images[j].cpu().numpy()
                    for o in range(image_j.shape[0]):    
                        image_filename = os.path.join(save_path, f"_image_{i*images.shape[0] + j}_{o}.png")
                        image_o = image_j[o].transpose(1, 2, 0)
                        plt.imshow(image_o)
                        plt.axis('off')
                        plt.savefig(image_filename)
                        plt.close()
        # 打印平均距离误差
        print("Average Pixel Distance: ", euclidean_distance_all / len(test_loader)) # 像素数
        print("Weighted Euclidean Distance: ", euclidean_distance_all_w / len(test_loader)) # 米数
        return running_loss / len(test_loader)

def train(model, dataloader, dataloader_test, criterion, optimizer, root_path, epochs=1, num=0):
    """
    训练模型
    参数:
        model: 要训练的模型
        dataloader: 训练数据加载器
        dataloader_test: 测试数据加载器
        criterion: 损失函数
        optimizer: 优化器
        root_path: 结果保存根路径
        epochs: 训练轮数
        num: 起始epoch编号(用于继续训练)
    返回:
        最佳模型权重路径
    """
    model.train()
    best_loss = float('inf')
    best_weight = ""
    BATCHSIZE = BATCHSIZE_
    ALLNUM = ALLNUM_
    IFUPDATA = 0
    root_save_path_weights = os.path.join(root_path, "save_weights/")
    log_file_path = os.path.join(root_path, "logs/", f"train_loss_and_test_loss_{current_time}.txt")
    
    print(f"日志文件将保存到: {log_file_path}")
    
    with open(log_file_path, "a") as log_file:
        log_file.write("Epoch, Train Loss, Test Loss, Base Loss, Shape Loss\n")
        for epoch in range(epochs):
            running_loss = 0.0
            base_loss_sum = 0.0
            shape_loss_sum = 0.0
            # 随机采样训练数据
            random_indices = set(random.sample(range(ALLNUM_FOR_TRAIN), ALLNUM))
            random_indices_list = list(random_indices)
            subset = Subset(dataloader.dataset, random_indices_list)
            if IFUPDATA == 0:
                subset_loader = DataLoader(subset, batch_size=BATCHSIZE, num_workers=32, prefetch_factor=1, shuffle=False)
            # 训练循环
            i = 0
            subset_iterator = iter(subset_loader)
            for _ in range(len(subset_loader)):
                images, trajectories, targets, _ = next(subset_iterator)
                print(f"epoch:{epoch}/{epochs}, dataloader:{i}/{len(subset_loader)}")
                i += 1
                images, trajectories, targets = images.to(device), trajectories.to(device), targets.to(device)
                optimizer.zero_grad()
                outputs = model(images, trajectories)
                # 计算损失
                traj_loss = TrajectoryLoss(alpha=1.0, beta=1.0)
                shape_loss = traj_loss(trajectories.view(-1, 10, 2) - outputs)
                outputs = outputs.view(-1, 20)
                targets = targets.view(-1, 20)
                base_loss = criterion(outputs, targets)
                loss = a * base_loss + b * shape_loss
                # 反向传播
                loss.backward()
                optimizer.step()
                # 统计损失
                base_loss_sum += base_loss.item()
                shape_loss_sum += shape_loss.item()
                running_loss += loss.item()
            # 计算平均损失
            avg_train_loss = running_loss / (ALLNUM/BATCHSIZE)
            avg_base_loss = base_loss_sum / (ALLNUM/BATCHSIZE)
            avg_shape_loss = shape_loss_sum / (ALLNUM/BATCHSIZE)
            print(f"Epoch {epoch+1}/{epochs}, Loss: {avg_train_loss}")
            # 测试
            test_loss = test(model, dataloader_test, IsSHOW=0)
            log_file.write(f"{epoch + num + 1}, {avg_train_loss:.4f}, {test_loss:.4f}, {avg_base_loss:.4f}, {avg_shape_loss:.4f}\n")
            log_file.flush()  # 立即刷新缓冲区，确保写入磁盘
            
            # 每10个epoch保存一次检查点
            if (epoch + 1) % 10 == 0:
                checkpoint_path = os.path.join(root_save_path_weights, f"checkpoint_epoch_{epoch+num+1}.pth")
                torch.save({
                    'epoch': epoch + num + 1,
                    'model_state_dict': model.state_dict(),
                    'optimizer_state_dict': optimizer.state_dict(),
                    'train_loss': avg_train_loss,
                    'test_loss': test_loss,
                }, checkpoint_path)
                print(f"检查点已保存: {checkpoint_path}")
            # 保存最佳模型
            if test_loss < best_loss:
                IFUPDATA = 1
                best_loss = test_loss
                clear_save_path(root_save_path_weights)
                # 从root_path中提取数据集名称
                dataset_name = root_path.split('/')[-2] if root_path.endswith('/') else root_path.split('/')[-1]
                best_weight = os.path.join(root_save_path_weights, f"best_model_{dataset_name}_epoch_{epoch+num+1}_loss_{best_loss:.4f}.pth")
                torch.save(model.state_dict(), best_weight)
                print(f"Saved model at epoch {epoch+1+num} with test_loss: {test_loss:.4f}")
            else:
                IFUPDATA = 0
    return best_weight

def resize_binary_image_manual_torch(images, target_size):
    """
    手动调整二值图像大小(Torch实现)
    参数:
        images: 输入图像张量
        target_size: 目标尺寸(高度,宽度)
    返回:
        调整大小后的图像张量
    """
    if images.shape[0] == 1:
        image = images[0,:,:]
    else:
        image = images
    if isinstance(image, np.ndarray):
        image = torch.tensor(image)
    original_height, original_width = image.shape
    target_height, target_width = target_size
    # 初始化新图像
    resized_image = torch.zeros((3, target_height, target_width), dtype=torch.float32)
    # 计算缩放比例
    scale_y = target_height / original_height
    scale_x = target_width / original_width
    # 映射值为1的像素
    ones_indices = torch.nonzero(image == 1, as_tuple=False)
    for idx in ones_indices:
        x, y = idx
        new_y = int(y.item() * scale_y)
        new_x = int(x.item() * scale_x)
        if 0 <= new_y < target_height and 0 <= new_x < target_width:
            resized_image[:, new_x, new_y] = 1
    return resized_image

class TrajectoryDataset(Dataset):
    """
    轨迹数据集类
    从.pth文件加载图像序列和轨迹数据
    """
    def __init__(self, data_dir):
        self.data_dir = data_dir
        self.file_list = sorted([os.path.join(data_dir, file) for file in os.listdir(data_dir) if file.endswith(".pth")])
        self.num_files = len(self.file_list)
        print(self.num_files)

    def __len__(self):
        return len(self.file_list)

    def __getitem__(self, idx):
        data_dict = torch.load(self.file_list[idx])
        images = data_dict['images']  # (10, 3, 840, 840)
        images_expanded = images.unsqueeze(1).repeat(1, 3, 1, 1)
        trajectory = data_dict['trajectory']  # (16,)
        target = data_dict['target']          # (16,)
        file_path = self.file_list[idx]      # 添加文件路径
        return images_expanded, trajectory, target, file_path

def load_data(data_dir, batch_size):
    """
    加载数据
    参数:
        data_dir: 数据目录
        batch_size: 批次大小
    返回:
        数据加载器
    """
    dataset = TrajectoryDataset(data_dir)
    dataloader = DataLoader(dataset, batch_size=batch_size, shuffle=True)
    return dataloader

# 主程序
if __name__ == "__main__":
    # 数据加载
    epochs_num = 15000
    batch_size_train = BATCHSIZE_
    batch_size_test = BATCHSIZE_Test
    batch_size_eva = 1

    # 数据路径
    data_dir1 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/train"
    data_dir2 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/test"
    data_dir3 = "data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/eva"

    # 创建数据加载器
    train_loader = load_data(data_dir1, batch_size_train)
    test_loader = load_data(data_dir2, batch_size_test)
    eva_loader = load_data(data_dir3, batch_size_eva)

    # 初始化模型
    model = DBResNet4(num_points=10).to(device)
    
    # 计算参数量
    def count_parameters(model):
        return sum(p.numel() for p in model.parameters())
    total_params = count_parameters(model)
    print(f'Total number of parameters: {total_params}')

    # 损失函数和优化器
    criterion = nn.MSELoss()
    optimizer = optim.Adam(model.parameters(), lr=0.001)

    # 加载预训练权重(如果存在)
    k = 0
    if 1: # 0 测试状态：无意义；训练状态：从零开始重新训练权重，1 加载预训练权重 测试状态：推理结果；训练状态：继续训练已有权重 
        checkpoint_path = "data_output/model_results/yk_9_err1/save_weights/3000/epoch=1265-step=184836.pth" # 预训练权重路径
        checkpoint = torch.load(checkpoint_path)
        model.load_state_dict(checkpoint)
        k = 7954
        print(f"Loaded checkpoint: k={k} path={checkpoint_path}")

    # 根据数据集动态设置保存路径
    if "data_yk_no9_err1" in data_dir1:
        root_path = "data_output/model_results/yk_9_err1/"
    elif "data_yk_no6_err1" in data_dir1:
        root_path = "data_output/model_results/yk_6_err1/"
    elif "data_yk_no7_err1" in data_dir1:
        root_path = "data_output/model_results/yk_7_err1/"
    elif "data_yk_no8_err1" in data_dir1:
        root_path = "data_output/model_results/yk_8_err1/"
    elif "data_yk_no11_err1" in data_dir1:
        root_path = "data_output/model_results/yk_11_err1/"
    elif "data_yk_no12_err1" in data_dir1:
        root_path = "data_output/model_results/yk_12_err1/"
    elif "data_yk_no14_err1" in data_dir1:
        root_path = "data_output/model_results/yk_14_err1/"
    else:
        # 默认路径，包含时间戳
        root_path = f"data_output/model_results/experiment_{current_time}/"
    
    # 创建所有必要的目录
    os.makedirs(root_path, exist_ok=True)
    save_weights_path = os.path.join(root_path, "save_weights/")
    save_images_path = os.path.join(root_path, "save_images/")
    save_logs_path = os.path.join(root_path, "logs/")
    os.makedirs(save_weights_path, exist_ok=True)
    os.makedirs(save_images_path, exist_ok=True)
    os.makedirs(save_logs_path, exist_ok=True)

    # 保存配置信息
    config_file_path = os.path.join(save_logs_path, f"config_{current_time}.json")
    config_info = {
        "dataset": data_dir1.split('/')[-3] if '/' in data_dir1 else "unknown",
        "batch_size": BATCHSIZE_,
        "batch_size_test": BATCHSIZE_Test,
        "learning_rate": 0.001,
        "epochs": epochs_num,
        "model_type": "DBResNet4",
        "num_points": 10,
        "data_paths": {
            "train": data_dir1,
            "eval": data_dir2,
            "test": data_dir3
        },
        "timestamp": current_time,
        "device": str(device),
        "total_parameters": total_params,
        "loss_weights": {
            "base_loss_weight": a,
            "shape_loss_weight": b
        }
    }
    
    with open(config_file_path, 'w') as f:
        json.dump(config_info, f, indent=4)
    print(f"配置信息已保存到: {config_file_path}")
    
    # 训练或测试
    if Train_state == 1:
        best_weight = train(model, train_loader, test_loader, criterion, optimizer, root_path, epochs=epochs_num, num=k)
        checkpoint_best_weight = torch.load(best_weight)
        model.load_state_dict(checkpoint_best_weight)
        print(f"Loaded best weight: {best_weight}")

    # 测试并保存图像和轨迹
    root_save_path_images = os.path.join(root_path, "save_images/")
    # 生成完整的子文件夹路径（包含时间戳）
    save_path_ = os.path.join(root_save_path_images, current_time)
    # 确保该路径存在，如果不存在则创建
    os.makedirs(save_path_, exist_ok=True)
    print(save_path_)
    #test_loss=test(model, eva_loader, save_path=save_path_, IsSHOW=0)
    test_loss=eva(model, eva_loader, save_path=save_path_, IsSHOW=0)