#!/usr/bin/env python3
"""
数据生成模块 - DBResNet磁导航轨迹预测系统
===================================================

功能说明:
1. 从原始惯导数据和磁图数据生成深度学习训练样本
2. 处理惯导轨迹、卫导轨迹和磁场测量数据
3. 生成包含轨迹序列和对应磁场图像的训练数据

主要处理流程:
- 加载磁图数据(.npz格式)并进行预处理
- 读取惯导轨迹和卫导参考轨迹数据
- 在磁图覆盖区域内匹配轨迹点和磁场值
- 生成包含10个点的轨迹序列作为训练样本
- 保存为.pth格式的深度学习数据文件

作者: DBResNet项目组
版本: v2.0
"""

import os
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.colors as mcolors
from scipy.ndimage import gaussian_filter
from scipy.ndimage import binary_dilation
import random
import torch

# 全局计数器，用于标识生成的数据样本
NUM = 0 

# 文件路径配置
# ====================================================================
# 惯导数据文件路径配置 - 包含纯惯导轨迹和惯卫融合轨迹
# 纯惯导轨迹: 含有惯导系统累积误差的轨迹数据
# 惯卫融合轨迹: 作为真值参考的高精度轨迹数据
# ====================================================================
file_paths = [
    # ================= 6号架次数据 ============
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\6#_pure_INS_optimized_1114\ins_pure_6_cut.csv',
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_6_2_cut.csv'
    
    # ================= 7号架次数据 ============
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\7#_pure_INS_optimized_1114\ins_pure_7_cut.csv',
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_7_2_cut.csv'

    # ================= 8号架次数据 ============
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\纯惯导\8#_pure_INS_optimized_1114\ins_pure_8_cut.csv',
    # r'D:\DC Navigation\20241106_ykguiji\惯导数据\惯卫融合用作惯导\avp_8_cut.csv'

    # ================= 9号架次数据 (当前使用) ============
    r'data_input/20241106_ykguiji/惯导数据/纯惯导/9#_pure_INS_optimized_1113/ins_pure_avp_9_whole_cut.csv',
    r'data_input/20241106_ykguiji/惯导数据/惯卫融合用作惯导/avp_9_2_cut.csv'
]

# 实测磁场数据文件路径 - CSV格式的磁场测量数据
file_path2 = r'data_input/fenkuan_map/20250312-210047572288_OK/altitude_3000_3D_in_zrc20250306_filt_move_average_w21_rm_direction_diff_aircraft_number_9.csv'

# 磁图模型文件路径 - NPZ格式的磁场栅格数据
file_path = r'data_input/fenkuan_map/20250312-210047572288_OK/pred_3000m_3D_in_reso_25m_model_OK_20250312-210047572288_20250312-212703783073.npz'

# 时间段配置 - 指定要处理的轨迹时间范围
# ====================================================================
# 各架次对应的飞行时间段
# 6号架次: 2024-09-01 13:40:50 ~ 16:30:20
# 7号架次: 2024-09-08 14:10:50 ~ 16:10:20  
# 8号架次: 2024-09-09 05:30:30 ~ 06:40:00
# 9号架次: 2024-09-09 14:10:00 ~ 16:10:00 (当前使用)
# ====================================================================
start_time = '2024-09-09 14:13:00'  # 轨迹处理开始时间
end_time = '2024-09-09 15:53:00'    # 轨迹处理结束时间


def map_to_fixed_values(data, thresholds, values):
    """
    将连续的数据映射到固定的离散值
    
    参数:
        data: 输入的连续数据数组
        thresholds: 阈值列表，必须按升序排列
        values: 映射的固定值列表，长度应为len(thresholds)+1
    
    返回:
        mapped_data: 映射后的离散数据数组
        
    功能说明:
        - 将连续的磁场数据量化为离散区间
        - 有助于减少噪声影响，提高数据的鲁棒性
        - 便于神经网络的特征学习
    """
    mapped_data = np.zeros_like(data)
    for i, threshold in enumerate(thresholds):
        if i == 0:
            # 处理第一个区间: data <= threshold
            mapped_data[data <= threshold] = values[i]
        else:
            # 处理中间区间: thresholds[i-1] < data <= threshold
            mapped_data[(data > thresholds[i-1]) & (data <= threshold)] = values[i]
    
    # 处理最后一个区间: data > thresholds[-1]
    mapped_data[data > thresholds[-1]] = values[-1]
    return mapped_data


def load_and_plot_magnetic_field(file_path):
    """
    加载并处理磁场图数据，使用三分量磁场数据生成RGB颜色映射
    
    参数:
        file_path: .npz格式的磁场数据文件路径
        
    返回:
        mag_z_mapped: 处理后的Z分量磁场数据 (用于可视化)
        lon: 经度网格数组
        lat: 纬度网格数组  
        mag_x_: X分量原始磁场数据
        mag_y_: Y分量原始磁场数据
        mag_z_: Z分量原始磁场数据
    
    处理流程:
        1. 加载NPZ文件中的磁场三分量数据
        2. 对每个分量进行归一化处理
        3. 应用高斯滤波减少噪声
        4. 将连续值映射为离散值
        5. 生成RGB颜色矩阵用于可视化
    """
    # 加载磁场数据文件
    data = np.load(file_path)
    
    # 提取经纬度网格和磁场三分量数据
    lat = data['lat']                    # 纬度数组 (840x840)
    lon = data['lon']                    # 经度数组 (840x840)
    magnetic_components = data['data']   # 磁场三分量数据 (840x840x3)
    
    print(f"磁场数据形状: {lat.shape}")
    
    # 分离三个磁场分量
    mag_x_ = magnetic_components[:, :, 0]  # X分量 (北向)
    mag_y_ = magnetic_components[:, :, 1]  # Y分量 (东向) 
    mag_z_ = magnetic_components[:, :, 2]  # Z分量 (垂直向下)
    
    # 数据归一化函数 - 将数据缩放到[0,1]范围
    def normalize(data):
        """将输入数组归一化到[0,1]范围"""
        return (data - np.min(data)) / (np.max(data) - np.min(data))
    
    # 对三个分量分别进行归一化
    mag_x_normalized = normalize(mag_x_)
    mag_y_normalized = normalize(mag_y_)
    mag_z_normalized = normalize(mag_z_)
    
    # 高斯滤波函数 - 平滑数据并减少噪声
    def apply_gaussian_filter(data, sigma=1):
        """应用高斯滤波平滑数据"""
        return gaussian_filter(data, sigma=sigma)
    
    # 对每个归一化后的分量应用高斯滤波 (sigma=2表示适度平滑)
    mag_x_filtered = apply_gaussian_filter(mag_x_normalized, sigma=2)
    mag_y_filtered = apply_gaussian_filter(mag_y_normalized, sigma=2)
    mag_z_filtered = apply_gaussian_filter(mag_z_normalized, sigma=2)
    
    # 设置量化参数 - 将连续值映射为20个离散区间
    thresholds = np.linspace(0, 1, 21)[1:-1]  # 19个阈值点，创建20个区间
    values = np.linspace(0.1, 1.0, 20)        # 20个映射值
    
    # 对滤波后的数据进行离散化映射
    mag_x_mapped = map_to_fixed_values(mag_x_filtered, thresholds, values)
    mag_y_mapped = map_to_fixed_values(mag_y_filtered, thresholds, values)
    mag_z_mapped = map_to_fixed_values(mag_z_filtered, thresholds, values)
    
    # 创建RGB颜色矩阵 - 用于可视化显示
    rgb_matrix = np.stack([mag_x_mapped, mag_y_mapped, mag_z_mapped], axis=-1)
    
    # 注释掉的可视化代码 - 可用于调试和查看磁场分布
    """
    # 创建网格用于绘图
    lon_grid = lon
    lat_grid = lat
    
    # 绘制磁场可视化图像
    plt.figure(figsize=(12, 8))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], 
               origin='lower', cmap='jet')
    plt.colorbar(label='Normalized Magnetic Field Components')
    plt.xlabel('Longitude')
    plt.ylabel('Latitude')
    plt.title('Magnetic Field Visualization')
    plt.grid(True)
    plt.show()
    """
    
    return mag_z_mapped, lon, lat, mag_x_, mag_y_, mag_z_


def read_XYZ_trajectory(file_path, start_time=None, end_time=None):
    """
    读取包含磁场测量数据的轨迹文件
    
    参数:
        file_path: CSV文件路径，包含磁场测量数据
        start_time: 筛选开始时间 (可选)
        end_time: 筛选结束时间 (可选)
        
    返回:
        DataFrame: 包含时间、位置和磁场测量数据的数据框
        
    数据格式说明:
        - Time: 时间戳 (YYYY-MM-DD HH:MM:SS)
        - Lat/Lon: 纬度/经度 (度)
        - Alt: 高度 (米)
        - X/Y/Z: 磁场三分量 (nT)
        - DeltaTotalfield: 总磁场异常 (nT)
        - FlightDirection: 飞行方向角 (度)
        - AircraftNumber: 飞机架次编号
    """
    # 定义CSV文件的列名
    columns = ['Time', 'Lat', 'Lon', 'Alt', 
              'X', 'Y', 'Z', 
              'DeltaTotalfield', 'FlightDirection', 'AircraftNumber']

    # 读取CSV文件，跳过第一行标题
    df = pd.read_csv(file_path, names=columns, skiprows=1)

    # 将时间列转换为pandas的datetime类型
    df['Time'] = pd.to_datetime(df['Time'])

    # 根据指定时间段筛选数据
    if start_time and end_time:
        start_time = pd.to_datetime(start_time)
        end_time = pd.to_datetime(end_time)
        df = df[(df['Time'] >= start_time) & (df['Time'] <= end_time)]
    
    return df


def read_ins_trajectory(file_path, start_time=None, end_time=None):
    """
    读取惯导系统轨迹数据
    
    参数:
        file_path: CSV文件路径，包含惯导轨迹数据
        start_time: 筛选开始时间 (可选)
        end_time: 筛选结束时间 (可选)
        
    返回:
        DataFrame: 包含惯导轨迹数据的数据框，新增度制经纬度列
        
    数据格式说明:
        - time: 时间戳
        - pitch/roll/yaw: 姿态角 (弧度)
        - v_e/v_n/v_u: 东北天速度分量 (m/s)
        - lat/lon: 纬度/经度 (弧度)
        - alt: 高度 (米)
        - lat_deg/lon_deg: 转换后的度制经纬度
    """
    # 定义惯导数据的列名
    columns = ['time', 'pitch', 'roll', 'yaw', 
              'v_e', 'v_n', 'v_u', 
              'lat', 'lon', 'alt']
    
    # 读取CSV文件
    df = pd.read_csv(file_path, names=columns)
    
    # 转换时间格式
    df['time'] = pd.to_datetime(df['time'])
    
    # 根据时间段筛选数据
    if start_time and end_time:
        start_time = pd.to_datetime(start_time)
        end_time = pd.to_datetime(end_time)
        df = df[(df['time'] >= start_time) & (df['time'] <= end_time)]
    
    # 将弧度制经纬度转换为度制 (惯导输出通常为弧度)
    df['lat_deg'] = np.degrees(df['lat'])
    df['lon_deg'] = np.degrees(df['lon'])
    
    return df


def haversine(lat1, lon1, lat2, lon2):
    """
    使用Haversine公式计算地球表面两点间的距离
    
    参数:
        lat1, lon1: 第一个点的纬度和经度 (度)
        lat2, lon2: 第二个点的纬度和经度 (度)
        
    返回:
        distance: 两点间的距离 (米)
        
    算法说明:
        Haversine公式考虑了地球的球面特性，
        相比简单的欧几里得距离计算更加精确
    """
    R = 6371e3  # 地球平均半径，单位：米
    
    # 将度转换为弧度
    phi1 = np.radians(lat1)
    phi2 = np.radians(lat2)
    delta_phi = np.radians(lat2 - lat1)
    delta_lambda = np.radians(lon2 - lon1)
    
    # Haversine公式计算
    a = (np.sin(delta_phi / 2) ** 2 + 
         np.cos(phi1) * np.cos(phi2) * np.sin(delta_lambda / 2) ** 2)
    c = 2 * np.arctan2(np.sqrt(a), np.sqrt(1 - a))
    
    return R * c  # 返回距离，单位：米


def Calculatingbias_internal(data1_lon, data1_lat, data2_lon, data2_lat):
    """
    计算两组轨迹数据内部的统计特性和偏差分析
    
    参数:
        data1_lon, data1_lat: 第一组轨迹的经纬度数据
        data2_lon, data2_lat: 第二组轨迹的经纬度数据
        
    功能:
        1. 计算每隔固定点数的轨迹段距离
        2. 分析轨迹在经纬度方向上的偏差
        3. 生成统计图表用于轨迹质量评估
        
    应用场景:
        - 评估惯导系统的漂移特性
        - 比较不同导航系统的性能
        - 轨迹数据质量检查
    """
    SKIP = 1000  # 采样间隔：每隔1000个点计算一次距离
    
    # 确保输入数据为numpy数组格式
    data1_lon = np.asarray(data1_lon)
    data1_lat = np.asarray(data1_lat)
    data2_lon = np.asarray(data2_lon)
    data2_lat = np.asarray(data2_lat)
    
    def compute_skip_distances(lat, lon, skip):
        """计算每隔skip个点的距离"""
        lat1, lat2 = lat[:-skip], lat[skip:]
        lon1, lon2 = lon[:-skip], lon[skip:]
        distances = [
            haversine(lat1[i], lon1[i], lat2[i], lon2[i])
            for i in range(len(lat1))
        ]
        return np.array(distances)
    
    def compute_skip_differences(lat, lon, skip):
        """计算经纬度方向的距离偏差"""
        lat1, lat2 = lat[:-skip], lat[skip:]
        lon1, lon2 = lon[:-skip], lon[skip:]
        
        # 纬度方向距离（保持经度不变）
        delta_lat = [
            haversine(lat1[i], lon1[i], lat2[i], lon1[i])
            for i in range(len(lat1))
        ]
        
        # 经度方向距离（保持纬度不变）
        delta_lon = [
            haversine(lat1[i], lon1[i], lat1[i], lon2[i])
            for i in range(len(lon1))
        ]
        
        return np.array(delta_lon), np.array(delta_lat)
    
    # 计算两组数据的间隔距离
    data1_skip_distances = compute_skip_distances(data1_lat, data1_lon, SKIP)
    data2_skip_distances = compute_skip_distances(data2_lat, data2_lon, SKIP)
    
    # 计算经纬度方向的距离偏差
    data1_skip_dx, data1_skip_dy = compute_skip_differences(data1_lat, data1_lon, SKIP)
    data2_skip_dx, data2_skip_dy = compute_skip_differences(data2_lat, data2_lon, SKIP)

    # 输出统计信息
    print(f"轨迹1 - 最大间隔距离 (skip={SKIP}): {np.max(data1_skip_distances):.2f} m, "
          f"最小距离: {np.min(data1_skip_distances):.2f} m, "
          f"平均距离: {np.mean(data1_skip_distances):.2f} m")
    print(f"轨迹2 - 最大间隔距离 (skip={SKIP}): {np.max(data2_skip_distances):.2f} m, "
          f"最小距离: {np.min(data2_skip_distances):.2f} m, "
          f"平均距离: {np.mean(data2_skip_distances):.2f} m")
    
    # 绘制统计分析图表
    plt.figure(figsize=(16, 10))
    
    # 子图1: 间隔距离比较
    plt.subplot(3, 1, 1)
    plt.plot(data1_skip_distances, label='轨迹1 间隔距离', color='blue')
    plt.plot(data2_skip_distances, label='轨迹2 间隔距离', color='orange')
    plt.xlabel('点索引')
    plt.ylabel('距离 (m)')
    plt.title(f'轨迹间隔-{SKIP}点距离比较')
    plt.legend()
    plt.grid()
    
    # 子图2: 经度方向偏差
    plt.subplot(3, 1, 2)
    plt.plot(data1_skip_dx, label='轨迹1 经度偏差 (m)', color='blue')
    plt.plot(data2_skip_dx, label='轨迹2 经度偏差 (m)', color='orange')
    plt.xlabel('点索引')
    plt.ylabel('经度方向偏差 (m)')
    plt.title(f'X方向偏差 (间隔-{SKIP}, 经度方向)')
    plt.legend()
    plt.grid()
    
    # 子图3: 纬度方向偏差
    plt.subplot(3, 1, 3)
    plt.plot(data1_skip_dy, label='轨迹1 纬度偏差 (m)', color='blue')
    plt.plot(data2_skip_dy, label='轨迹2 纬度偏差 (m)', color='orange')
    plt.xlabel('点索引')
    plt.ylabel('纬度方向偏差 (m)')
    plt.title(f'Y方向偏差 (间隔-{SKIP}, 纬度方向)')
    plt.legend()
    plt.grid()
    
    plt.tight_layout()
    plt.show()


def Calculatingbias(file_paths1, file_paths2, start_time, end_time):
    """
    计算并可视化两条轨迹之间的整体偏差
    
    参数:
        file_paths1, file_paths2: 两个轨迹文件的路径
        start_time, end_time: 分析的时间段
        
    功能:
        1. 读取两条轨迹数据
        2. 计算点对点之间的距离偏差
        3. 统计偏差的最大值、最小值和平均值
        4. 生成偏差分析图表
        5. 调用内部偏差分析函数
    """
    # 读取两条轨迹的数据
    df1 = read_ins_trajectory(file_paths1, start_time=start_time, end_time=end_time)
    df2 = read_ins_trajectory(file_paths2, start_time=start_time, end_time=end_time)
    
    # 提取经纬度数据
    data1_lon = df1['lon_deg']
    data1_lat = df1['lat_deg']
    data2_lon = df2['lon_deg']
    data2_lat = df2['lat_deg']
    
    # 调用内部偏差分析函数
    Calculatingbias_internal(data1_lon, data1_lat, data2_lon, data2_lat)

    # 计算每一对对应点之间的距离
    distances = haversine(data1_lat, data1_lon, data2_lat, data2_lon)

    # 统计距离偏差
    max_distance = np.max(distances)
    min_distance = np.min(distances)
    mean_distance = np.mean(distances)

    # 输出统计结果
    print(f"最大偏差距离: {max_distance:.2f} m")
    print(f"最小偏差距离: {min_distance:.2f} m")
    print(f"平均偏差距离: {mean_distance:.2f} m")

    # 绘制点对点距离偏差曲线
    plt.figure(figsize=(12, 6))
    plt.plot(distances, label='点对点距离偏差', color='blue')
    plt.axhline(max_distance, color='red', linestyle='--', 
                label=f'最大值: {max_distance:.2f} m')
    plt.axhline(min_distance, color='green', linestyle='--', 
                label=f'最小值: {min_distance:.2f} m')
    plt.axhline(mean_distance, color='orange', linestyle='--', 
                label=f'平均值: {mean_distance:.2f} m')
    plt.xlabel('点索引')
    plt.ylabel('距离偏差 (m)')
    plt.title('点对点距离偏差分析')
    plt.legend()
    plt.grid()
    plt.show()


def plot_trajectories(file_paths, start_time, end_time, mag_z_mapped, lon, lat, data):
    """
    在磁场背景图上绘制多条轨迹的可视化图
    
    参数:
        file_paths: 轨迹文件路径列表
        start_time, end_time: 时间范围
        mag_z_mapped: 磁场Z分量背景图
        lon, lat: 经纬度网格
        data: 磁场测量数据
        
    功能:
        1. 以磁场图为背景
        2. 叠加显示多条轨迹
        3. 标记轨迹起点和终点
        4. 保存可视化结果
    """
    # 可选：计算轨迹间偏差
    # Calculatingbias(file_paths[0], file_paths[1], start_time, end_time)

    # 创建图形并显示磁场背景
    plt.figure(figsize=(12, 10))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], 
               origin='lower', cmap='jet')
    
    # 绘制磁场测量轨迹
    plt.plot(data['Lon'], data['Lat'], linewidth=4, label='磁场测量轨迹')
    
    # 处理并绘制每条轨迹
    for file_path in file_paths:
        try:
            # 读取轨迹数据
            df = read_ins_trajectory(file_path, start_time=start_time, end_time=end_time)
            
            # 绘制轨迹线
            plt.plot(df['lon_deg'], df['lat_deg'], linewidth=1.5, 
                    label=os.path.basename(file_path))
            
            # 标记起点和终点
            if not df.empty:
                plt.plot(df['lon_deg'].iloc[0], df['lat_deg'].iloc[0], 
                        'go', markersize=8)   # 起点：绿色圆点
                plt.plot(df['lon_deg'].iloc[-1], df['lat_deg'].iloc[-1], 
                        'ro', markersize=8)   # 终点：红色圆点
                        
        except Exception as e:
            print(f"轨迹文件处理失败: {file_path}, 错误: {str(e)}")
    
    # 设置图表属性
    plt.grid(True)
    plt.xlabel('经度 (度)')
    plt.ylabel('纬度 (度)') 
    plt.title('惯导轨迹可视化')
    plt.legend()

    # 保存图片到当前脚本目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    save_path = os.path.join(current_dir, 'ins_trajectories.png')
    # plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.show()


def plot_deff(diff_x, diff_y, diff_z):
    """
    绘制磁场三分量差异曲线
    
    参数:
        diff_x, diff_y, diff_z: 磁场X、Y、Z分量的差异数组
        
    功能:
        可视化磁场测量值与参考值之间的差异，
        用于评估磁场数据的质量和一致性
    """
    # 创建点索引数组
    point_indices = np.arange(len(diff_x))
    
    # 创建图形
    fig, ax = plt.subplots(figsize=(10, 7))
    
    # 绘制三个分量的差异曲线
    ax.plot(point_indices, diff_x, label='X分量差异', color='red')
    ax.plot(point_indices, diff_y, label='Y分量差异', color='green')
    ax.plot(point_indices, diff_z, label='Z分量差异', color='blue')
    
    # 设置图表属性
    ax.set_title('磁场X、Y、Z分量差异分析')
    ax.set_xlabel('点索引')
    ax.set_ylabel('差异值')
    ax.legend()
    ax.grid(True)
    
    plt.tight_layout()
    plt.show()


def SegmentStatistics(ori_np, Lab_np):
    """
    对比分析实测磁场数据与参考磁场数据的统计特性
    
    参数:
        ori_np: 实测磁场数据数组 (N, 3)
        Lab_np: 参考磁场数据数组 (N, 3)
        
    功能:
        1. 计算磁场三分量的差异统计
        2. 分析不同误差范围内的数据百分比
        3. 输出最大、最小和平均差异值
        
    应用:
        评估磁场测量精度和数据质量
    """
    # 计算三个分量的差异
    diff_x = ori_np[:, 0] - Lab_np[:, 0]
    diff_y = ori_np[:, 1] - Lab_np[:, 1]
    diff_z = ori_np[:, 2] - Lab_np[:, 2]

    # 可选：绘制差异曲线
    # plot_deff(diff_x, diff_y, diff_z)
    
    # 计算绝对差异
    abs_diff_x = np.abs(diff_x)
    abs_diff_y = np.abs(diff_y)
    abs_diff_z = np.abs(diff_z)

    # 定义误差范围 (1-10 nT)
    ranges = range(1, 11)
    
    # 初始化结果字典
    results = {f'diff_{dim}': {i: 0 for i in ranges} for dim in ['x', 'y', 'z']}
    
    # 计算各误差范围内的数据百分比
    for r in ranges:
        results['diff_x'][r] = np.mean(abs_diff_x <= r) * 100
        results['diff_y'][r] = np.mean(abs_diff_y <= r) * 100
        results['diff_z'][r] = np.mean(abs_diff_z <= r) * 100
        
    # 输出百分比统计结果
    for diff, range_dict in results.items():
        print(f"{diff}分量在各误差范围内的百分比:")
        for r, percentage in range_dict.items():
            print(f"\t{r}nT以内: {percentage:.2f}%")
            
    # 计算各分量的统计特性
    stats = {
        'X': {'max': np.max(diff_x), 'min': np.min(diff_x), 'mean': np.mean(abs_diff_x)},
        'Y': {'max': np.max(diff_y), 'min': np.min(diff_y), 'mean': np.mean(abs_diff_y)},
        'Z': {'max': np.max(diff_z), 'min': np.min(diff_z), 'mean': np.mean(abs_diff_z)}
    }
    
    # 输出统计结果
    for component, values in stats.items():
        print(f"{component}分量统计:")
        print(f"  最大差异: {values['max']:.6f} nT")
        print(f"  最小差异: {values['min']:.6f} nT")
        print(f"  平均差异: {values['mean']:.6f} nT")


def visualize_sample(data_dir, image_size=224):
    """
    随机读取并可视化一个生成的训练样本
    
    参数:
        data_dir: 训练数据目录
        image_size: 图像尺寸 (默认224x224)
        
    功能:
        1. 随机选择一个.pth训练文件
        2. 加载轨迹数据和噪声数据
        3. 可视化真实轨迹、噪声轨迹和候选点位置
        4. 用于验证数据生成质量
    """
    # 获取所有.pth数据文件
    files = [f for f in os.listdir(data_dir) if f.endswith('.pth')]
    if not files:
        print("目录中未找到数据文件!")
        return

    # 随机选择一个文件
    selected_file = random.choice(files)
    print(f"随机选中文件: {selected_file}")

    # 加载训练数据
    data_path = os.path.join(data_dir, selected_file)
    data = torch.load(data_path)

    # 提取数据组件
    images = data['images'].numpy()              # 图像序列 (10, 3, 224, 224)
    noisy_trajectory = data['trajectory'].numpy().reshape(-1, 2)  # 噪声轨迹 (10, 2)
    noise = data['target'].numpy().reshape(-1, 2)                 # 噪声向量 (10, 2)
    real_trajectory = noisy_trajectory - noise                     # 真实轨迹

 
    # 设置matplotlib中文字体支持
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 设置中文字体
    plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题

    # 创建可视化图形
    plt.figure(figsize=(12, 10))
    
    # 绘制真实轨迹和噪声轨迹
    plt.plot(real_trajectory[:, 0], real_trajectory[:, 1], 'bo-', label='真实轨迹')
    plt.plot(noisy_trajectory[:, 0], noisy_trajectory[:, 1], 'go-', label='噪声轨迹')

    # 绘制候选点（从图像中提取）
    for i in range(len(images)):
        candidate_points = np.argwhere(images[i] == 1)  # 提取值为1的像素坐标
        plt.scatter(candidate_points[:, 0], candidate_points[:, 1], 
                   s=10, label=f"候选点 (点{i+1})")

    # 设置图表属性
    plt.title("轨迹和候选点可视化", fontsize=16)
    plt.xlabel("X坐标", fontsize=12)
    plt.ylabel("Y坐标", fontsize=12)
    plt.legend(loc='best', fontsize=10)
    plt.grid(True)
    
    # 保存可视化结果
    plt.savefig("trajectory_plot.png", dpi=300) 
    plt.show()


def savedata(X_LAB, Y_LAB, Z_LAB, trajectory_error, trajectory_mag, trajectory_true, 
             num, data_dir, i, best_region):
    """
    保存单个训练样本到.pth文件
    
    参数:
        X_LAB, Y_LAB, Z_LAB: 磁场三分量参考数据 [行(y), 列(x)]
        trajectory_error: 含误差的轨迹坐标
        trajectory_mag: 轨迹对应的磁场值
        trajectory_true: 真实轨迹坐标
        num: 样本编号
        data_dir: 保存目录
        i: 轨迹起始点索引
        best_region: 最佳匹配区域坐标
        
    返回:
        1: 保存成功标志
        
    功能:
        1. 根据轨迹磁场值生成候选点图像序列
        2. 计算轨迹坐标的平移量以确保在图像范围内
        3. 保存包含图像、轨迹和误差信息的训练样本
    """
    # 图像生成参数
    image_size = 224  # 图像尺寸
    tolerance_x = 1   # X分量容差 (nT)
    tolerance_y = 2   # Y分量容差 (nT)
    tolerance_z = 1   # Z分量容差 (nT)
    
    images = []
    
    # 为轨迹上的每个点生成候选点图像
    for point in trajectory_mag:
        mag_x_val, mag_y_val, mag_z_val = point
        
        # 创建空白图像
        image = np.zeros((image_size, image_size), dtype=np.uint8)
        
        # 计算磁场值差异 (利用NumPy广播)
        diff_x = np.abs(X_LAB - mag_x_val)
        diff_y = np.abs(Y_LAB - mag_y_val)
        diff_z = np.abs(Z_LAB - mag_z_val)
        
        # 根据容差条件生成候选点掩码
        if 0:  # 特殊情况处理（已注释）
            mask = (diff_y <= 4) & (diff_z - 7 <= tolerance_z)
        else:
            # 标准容差条件：三个分量都在容差范围内
            mask = ((diff_x <= tolerance_x) & 
                   (diff_y <= tolerance_y) & 
                   (diff_z <= tolerance_z))
        
        # 转置掩码以匹配图像坐标系
        mask_T = mask.T
        image[mask_T] = 1  # 将满足条件的像素设为1
        
        # 添加到图像序列
        images.append(image.copy())
    
    # 转换为numpy数组
    image_sequence = np.array(images)

    # 坐标平移计算 - 确保轨迹在图像范围内
    effective_size = 224 - 100  # 有效图像区域
    min_x, min_y = np.array(trajectory_error).min(axis=0)
    max_x, max_y = np.array(trajectory_error).max(axis=0)
    
    # 计算所需的平移量
    translation_x = 0
    translation_y = 0
    
    if min_x < 0:
        translation_x = -min_x  # 左边界超出时的平移
    elif max_x >= effective_size:
        translation_x = effective_size - max_x  # 右边界超出时的平移
        
    if min_y < 0:
        translation_y = -min_y  # 上边界超出时的平移
    elif max_y >= effective_size:
        translation_y = effective_size - max_y  # 下边界超出时的平移
    
    # 应用平移
    move = np.array([translation_x, translation_y])
    trajectory_error += move
    
    # 计算噪声向量 (误差轨迹 - 真实轨迹)
    noise = np.array(trajectory_error) - np.array(trajectory_true)

    # 创建样本标识和区域信息
    NUM_ID = np.array([num, i])
    region = np.array(best_region)
    
    # 构建训练样本字典
    data_dict = {
        'images': torch.tensor(image_sequence, dtype=torch.float32),       # 图像序列 (10, 224, 224)
        'trajectory': torch.tensor(trajectory_error.flatten(), dtype=torch.float32),  # 轨迹坐标 (20,)
        'target': torch.tensor(noise.flatten(), dtype=torch.float32),      # 目标噪声 (20,)
        'move': torch.tensor(move, dtype=torch.float32),                   # 平移量 (2,)
        'id': torch.tensor(NUM_ID, dtype=torch.float32),                   # 样本ID (2,)
        'best_region': torch.tensor(region.flatten(), dtype=torch.float32) # 区域信息 (4,)
    }

    # 保存到文件
    global NUM
    sample_id = NUM + num
    file_path = os.path.join(data_dir, f'tr_{sample_id}.pth')
    torch.save(data_dict, file_path)
    print(f"样本 {sample_id} 已保存")
    
    return 1


def FindTrajectory(start_idx, pos_wd, len_points, step, region):
    """
    从指定起始点开始构建指定长度的轨迹序列
    
    参数:
        start_idx: 起始点索引
        pos_wd: 位置数据列表 [[x, y, timestamp], ...]
        len_points: 需要的轨迹点数
        step: 相邻点间最小距离（像素）
        region: 有效区域边界 [y_min, y_max, x_min, x_max]
        
    返回:
        trajectory: 构建的轨迹点列表
        valid_flag: 轨迹是否有效的布尔值
        
    功能:
        1. 检查起始点是否在有效区域内
        2. 按照最小间距要求选择后续轨迹点
        3. 确保所有轨迹点都在指定区域内
        4. 将坐标转换为相对于区域左上角的局部坐标
    """
    # 初始化轨迹，只保留x和y坐标
    trajectory = [pos_wd[start_idx][:2]]  
    current_idx = start_idx
    current_point = pos_wd[start_idx][:2]
    
    # 提取区域边界
    x_max = region[3]
    x_min = region[2]
    y_max = region[1]
    y_min = region[0]
    
    # 检查起始点是否在区域内
    start_point = trajectory[0]
    if not (x_min <= start_point[0] < x_max and y_min <= start_point[1] < y_max):
        return [], False  # 起始点超出范围
    
    # 逐个寻找后续轨迹点
    for _ in range(1, len_points):
        for i in range(current_idx + 1, len(pos_wd)):
            # 候选点坐标
            next_point = pos_wd[i][:2]
            
            # 计算与当前点的距离
            dist = np.linalg.norm(np.array(next_point) - np.array(current_point))
            
            # 如果距离满足最小间距要求
            if dist >= step:
                trajectory.append(next_point)
                current_point = next_point
                current_idx = i
                break
    
    # 检查所有轨迹点是否都在区域内
    for point in trajectory:
        if not (x_min <= point[0] < x_max and y_min <= point[1] < y_max):
            return trajectory, False  # 有点超出边界
    
    # 检查轨迹长度是否足够
    if len(trajectory) < len_points:
        return trajectory, False
    
    # 转换为相对坐标（相对于区域左上角）
    trajectory = [[point[0] - x_min, point[1] - y_min] for point in trajectory]
    
    return trajectory, True


def generate_mag_and_error_trajectory(trajectory_true, Xmap, Ymap, Zmap, 
                                    MAP_GD_X, MAP_GD_Y, map_type, region):
    """
    根据真实轨迹生成对应的磁场值序列和误差轨迹
    
    参数:
        trajectory_true: 真实轨迹坐标列表
        Xmap, Ymap, Zmap: 磁场三分量映射数组
        MAP_GD_X, MAP_GD_Y: 惯导误差坐标映射
        map_type: 映射类型 (0或1，决定坐标索引方式)
        region: 区域边界信息
        
    返回:
        trajectory_mag: 轨迹对应的磁场值序列
        trajectory_error: 含误差的轨迹坐标序列
        
    功能:
        1. 根据轨迹坐标查找对应的磁场值
        2. 获取对应的误差轨迹坐标
        3. 处理不同的坐标索引方式
        4. 转换为相对坐标系统
    """
    trajectory_mag = []
    trajectory_error = []
    
    # 提取区域边界
    x_max = region[3]
    x_min = region[2]
    y_max = region[1]
    y_min = region[0]
    
    # 处理轨迹上的每个点
    for point in trajectory_true:
        x, y = point
        
        # 检查坐标是否在磁场数据范围内
        if 0 <= x < Xmap.shape[0] and 0 <= y < Ymap.shape[1]:
            # 转换为全局坐标
            xtemp = x + x_min
            ytemp = y + y_min

            # 根据映射类型获取磁场值
            if map_type == 0:  # 标准索引方式
                X_val = Xmap[xtemp, ytemp]
                Y_val = Ymap[xtemp, ytemp]
                Z_val = Zmap[xtemp, ytemp]
            elif map_type == 1:  # 转置索引方式
                X_val = Xmap[ytemp, xtemp]
                Y_val = Ymap[ytemp, xtemp]
                Z_val = Zmap[ytemp, xtemp]
            
            # 添加磁场值到序列
            trajectory_mag.append([X_val, Y_val, Z_val])
            
            # 添加对应的误差坐标
            trajectory_error.append([MAP_GD_X[xtemp, ytemp], MAP_GD_Y[xtemp, ytemp]])
            
        else:
            # 坐标超出范围时抛出异常
            raise IndexError(f"坐标 ({x}, {y}) 超出磁场数据有效范围！")

    # 转换误差轨迹为相对坐标
    trajectory_error = [[point[0] - x_min, point[1] - y_min] for point in trajectory_error]
    
    return trajectory_mag, trajectory_error


# 候选区域配置 - 不同飞行架次的区域设置
# ============================================================================
# 区域格式: [y_min, y_max, x_min, x_max]
# 每个区域大小: 224x224像素，对应实际地理范围约5.6km x 5.6km
# ============================================================================

# 6号架次候选区域配置
regions_6 = [
    [550, 774, 550, 774],  # 区域1: 右上角
    [500, 724, 550, 774],  # 区域2: 中上角 
    [400, 624, 550, 774],  # 区域3: 中部
    [300, 524, 550, 774],  # 区域4: 中下角
    [200, 424, 550, 774],  # 区域5: 下部
    [100, 324, 550, 774],  # 区域6: 左下角
    [0, 224, 550, 774],    # 区域7: 左上角
    [550, 774, 430, 654],  # 区域8: 右上角(中)
    [500, 724, 430, 654],  # 区域9: 中上角(中)
    [400, 624, 430, 654],  # 区域10: 中部(中)
    [300, 524, 430, 654],  # 区域11: 中下角(中)
    [200, 424, 430, 654],  # 区域12: 下部(中)
    [100, 324, 430, 654],  # 区域13: 左下角(中)
    [0, 224, 430, 654]     # 区域14: 左上角(中)
]

# 7号架次候选区域配置
regions_7 = [
    [550, 774, 256, 480],  # 区域1
    [500, 724, 256, 480],  # 区域2
    [400, 624, 256, 480],  # 区域3
    [300, 524, 256, 480],  # 区域4
    [200, 424, 256, 480],  # 区域5
    [100, 324, 256, 480],  # 区域6
    [0, 224, 256, 480]     # 区域7
]

# 8号架次候选区域配置
regions_8 = [
    [550, 774, 126, 350],  # 区域1
    [500, 724, 126, 350],  # 区域2
    [400, 624, 126, 350],  # 区域3
    [300, 524, 126, 350],  # 区域4
    [200, 424, 126, 350],  # 区域5
    [100, 324, 126, 350],  # 区域6
    [0, 224, 126, 350]     # 区域7
]

# 9号架次候选区域配置 (当前使用)
regions_9 = [
    [550, 774, 0, 224],    # 区域1: 右上角
    [500, 724, 0, 224],    # 区域2: 中上角
    [400, 624, 0, 224],    # 区域3: 中部
    [300, 524, 0, 224],    # 区域4: 中下角
    [200, 424, 0, 224],    # 区域5: 下部
    [100, 324, 0, 224],    # 区域6: 左下角
    [0, 224, 0, 224],      # 区域7: 左上角
]

# 当前使用的区域配置
regions = regions_9


def point_in_region(x, y, region):
    """
    判断点是否在矩形区域内
    
    参数:
        x, y: 点坐标
        region: 区域边界 [y_min, y_max, x_min, x_max]
        
    返回:
        bool: 点是否在区域内
    """
    y_min, y_max, x_min, x_max = region
    return x_min <= x < x_max and y_min <= y < y_max


def distance_to_boundary(x, y, region):
    """
    计算点到矩形区域边界的最近距离
    
    参数:
        x, y: 点坐标
        region: 区域边界
        
    返回:
        float: 到边界的最小距离
        
    说明:
        返回到上下边界的最小距离，用于选择最稳定的区域
    """
    y_min, y_max, x_min, x_max = region
    top = y - y_min      # 到上边界距离
    bottom = y_max - y   # 到下边界距离
    return min(top, bottom)


def find_farthest_region(x, y, regions):
    """
    找到包含指定点且距离边界最远的区域
    
    参数:
        x, y: 点坐标
        regions: 候选区域列表
        
    返回:
        best_region: 最佳区域边界，如果没有合适区域则返回None
        
    策略:
        选择距离边界最远的区域可以提供更多的轨迹扩展空间，
        减少轨迹超出区域边界的风险
    """
    farthest_distance = -np.inf
    best_region = None
    
    print(f"查找点 ({x}, {y}) 的最佳区域")
    
    for i, region in enumerate(regions):
        if point_in_region(x, y, region):
            dist = distance_to_boundary(x, y, region)
            if dist > farthest_distance:
                farthest_distance = dist
                best_region = region
                
    return best_region


def DataGeneration(file_path_GD, file_path_WD, start_time, end_time, 
                  lon, lat, X_LAB, Y_LAB, Z_LAB, data_CL, mag_z_mapped, data_dir):
    """
    主要数据生成函数 - 生成深度学习训练样本
    
    参数:
        file_path_GD: 惯导轨迹文件路径
        file_path_WD: 卫导参考轨迹文件路径
        start_time, end_time: 处理时间范围
        lon, lat: 磁图经纬度网格
        X_LAB, Y_LAB, Z_LAB: 磁场三分量参考数据
        data_CL: 实测磁场数据
        mag_z_mapped: 磁场Z分量可视化数据
        data_dir: 输出目录
        
    功能流程:
        1. 读取惯导和卫导轨迹数据
        2. 建立磁场测量数据的空间映射
        3. 构建轨迹-磁场对应关系
        4. 在候选区域内生成训练样本
        5. 保存样本到指定目录
    """
    print(f"磁场数据形状: {mag_z_mapped.shape}")
    print(f"磁场数据类型: {type(mag_z_mapped)}")
    
    # 读取惯导和卫导轨迹数据
    df_GD = read_ins_trajectory(file_path_GD, start_time=start_time, end_time=end_time)  # 惯导轨迹
    df_WD = read_ins_trajectory(file_path_WD, start_time=start_time, end_time=end_time)  # 卫导轨迹
    print(f"惯导数据点数: {len(df_GD)}, 卫导数据点数: {len(df_WD)}")
    
    # 初始化坐标映射数组
    MAP_WD_LON = np.zeros((840, 840), dtype=np.float32)  # 卫导经度映射
    MAP_WD_Lat = np.zeros((840, 840), dtype=np.float32)  # 卫导纬度映射
    MAP_GD_X = np.zeros((840, 840), dtype=np.int32)      # 惯导X坐标映射
    MAP_GD_Y = np.zeros((840, 840), dtype=np.int32)      # 惯导Y坐标映射
    
    # 实测磁场数据映射数组
    X_MAP_CL = np.zeros((840, 840), dtype=np.float32)    # X分量实测值
    Y_MAP_CL = np.zeros((840, 840), dtype=np.float32)    # Y分量实测值
    Z_MAP_CL = np.zeros((840, 840), dtype=np.float32)    # Z分量实测值
    
    # 建立实测磁场数据的空间映射
    for i in range(len(data_CL['Lon'])):
        lon_ = data_CL['Lon'][i]
        lat_ = data_CL['Lat'][i]
        
        # 将地理坐标转换为像素坐标
        x_ = (lon_ - lon[0, 0]) / (lon[839, 839] - lon[0, 0]) * 840.0
        y_ = (lat_ - lat[0, 0]) / (lat[839, 839] - lat[0, 0]) * 840.0

        x_idx = int(round(x_))
        y_idx = int(round(y_))

        # 存储实测磁场数据
        X_MAP_CL[x_idx, y_idx] = data_CL['X'][i]
        Y_MAP_CL[x_idx, y_idx] = data_CL['Y'][i]
        Z_MAP_CL[x_idx, y_idx] = data_CL['Z'][i]

    # 构建轨迹位置序列
    pos_wd = []  # 卫导位置序列
    last_x = 0
    last_y = 0
    
    # 每隔10个点采样一次，避免数据过于密集
    for i in range(0, len(df_WD['lon_deg']), 10):
        lon_ = df_WD['lon_deg'].iloc[i]
        lat_ = df_WD['lat_deg'].iloc[i]
        
        # 地理坐标转像素坐标
        x_ = (lon_ - lon[0, 0]) / (lon[839, 839] - lon[0, 0]) * 840.0
        y_ = (lat_ - lat[0, 0]) / (lat[839, 839] - lat[0, 0]) * 840.0
        
        y_idx = int(round(x_))  # 注意：坐标转换
        x_idx = int(round(y_))

        # 避免重复点，只添加新位置
        if i == 0:
            pos_wd.append([int(round(x_)), int(round(y_)), i])
            last_x = int(round(x_))
            last_y = int(round(y_))
        elif not (y_idx == last_x and x_idx == last_y):
            pos_wd.append([int(round(x_)), int(round(y_)), i])
            last_x = int(round(x_))
            last_y = int(round(y_))
            
        # 建立卫导-惯导坐标对应关系
        if 0 <= x_idx < 840 and 0 <= y_idx < 840:
            # 获取对应的惯导坐标
            lon_gd = df_GD['lon_deg'].iloc[i]
            lat_gd = df_GD['lat_deg'].iloc[i]
            x_t = (lon_gd - lon[0, 0]) / (lon[839, 839] - lon[0, 0]) * 840.0
            y_t = (lat_gd - lat[0, 0]) / (lat[839, 839] - lat[0, 0]) * 840.0
                
            # 存储坐标映射关系
            MAP_WD_LON[y_idx, x_idx] = df_WD['lon_deg'].iloc[i]
            MAP_WD_Lat[y_idx, x_idx] = df_WD['lat_deg'].iloc[i]
            MAP_GD_X[y_idx, x_idx] = int(round(x_t))  # 惯导对应位置
            MAP_GD_Y[y_idx, x_idx] = int(round(y_t))

    print(f"卫导位置点数: {len(pos_wd)}")
    non_zero_count = np.count_nonzero(MAP_GD_X)
    print(f"有效坐标映射数量: {non_zero_count}")

    # 开始生成训练样本
    num = 0  # 样本计数器
    
    with open('output_data.txt', 'a') as file:
        for i in range(len(pos_wd)):
            x = pos_wd[i][0]  # 列坐标
            y = pos_wd[i][1]  # 行坐标
            
            # 找到包含该点的最佳区域
            best_region = find_farthest_region(x, y, regions)
            if best_region is None:
                continue  # 如果没有合适区域，跳过该点
                
            # 提取区域边界
            Xmax = best_region[3]
            Xmin = best_region[2]
            Ymax = best_region[1]
            Ymin = best_region[0]

            # 检查点是否在选定区域内
            if Ymin <= y <= Ymax and Xmin <= x <= Xmax:
                # 在可视化图上标记该点
                mag_z_mapped[y-5:y+5, x-5:x+5] = 0.8
                
                # 尝试构建从该点开始的轨迹
                trajectory_true, ValidMark = FindTrajectory(i, pos_wd, 10, 6, best_region)
                
                if ValidMark:  # 如果轨迹有效
                    # 生成轨迹对应的磁场值和误差信息
                    trajectory_mag, trajectory_error = generate_mag_and_error_trajectory(
                        trajectory_true, X_MAP_CL, Y_MAP_CL, Z_MAP_CL, 
                        MAP_GD_X, MAP_GD_Y, 0, best_region)
                    
                    # 保存训练样本
                    savedata(X_LAB[Ymin:Ymax, Xmin:Xmax], 
                            Y_LAB[Ymin:Ymax, Xmin:Xmax], 
                            Z_LAB[Ymin:Ymax, Xmin:Xmax],
                            trajectory_error, trajectory_mag, trajectory_true, 
                            num, data_dir, i, best_region)
                    
                    # 记录样本信息
                    file.write(f"{num},{Xmax},{Xmin},{Ymax},{Ymin}\n")
                    num += 1

    # 可选的可视化代码（已注释）
    """
    plt.figure(figsize=(12, 10))
    plt.imshow(mag_z_mapped, extent=[lon.min(), lon.max(), lat.min(), lat.max()], 
               origin='lower', cmap='jet')
    plt.plot(df_WD['lon_deg'], df_WD['lat_deg'], linewidth=1.5, label="卫导轨迹")
    plt.plot(df_GD['lon_deg'], df_GD['lat_deg'], linewidth=1.5, label="惯导轨迹")
    plt.plot(data_CL['Lon'], data_CL['Lat'], linewidth=4, label='磁场测量轨迹')
    plt.legend()
    plt.show()
    """


def main():
    """
    主函数 - 控制整个数据生成流程
    
    功能:
        1. 设置数据输出目录
        2. 加载磁场数据和轨迹数据
        3. 调用数据生成函数
        4. 可视化生成的样本
    """
    # 设置输出目录
    data_dir = r"data_output/data_yk_sc_err_1_test/9/data_yk_no9_err1/train"
    
    # 可选：可视化已有样本
    while(0):
        visualize_sample(data_dir)
    
    # 创建输出目录
    if not os.path.exists(data_dir):
        os.makedirs(data_dir)
    
    # 加载磁场数据
    rgb_matrix, lon, lat, X_LAB, Y_LAB, Z_LAB = load_and_plot_magnetic_field(file_path)

    # 读取实测磁场数据
    data = read_XYZ_trajectory(file_path2)

    # 可选的数据滤波处理（已注释）
    """
    # 移动平均滤波示例
    def moving_average_history_cumsum(data, window_size=3):
        \"\"\"基于历史值的移动平均滤波\"\"\"
        cumsum_vec = np.cumsum(np.insert(data, 0, 0))
        return (cumsum_vec[window_size:] - cumsum_vec[:-window_size]) / window_size
    
    window_size = 10
    start_index = window_size - 1
    filtered_X = moving_average_history_cumsum(data['X'], window_size)
    filtered_Y = moving_average_history_cumsum(data['Y'], window_size)
    filtered_Z = moving_average_history_cumsum(data['Z'], window_size)
    
    data.loc[start_index:(len(filtered_X)+start_index-1), 'X'] = filtered_X
    data.loc[start_index:(len(filtered_X)+start_index-1), 'Y'] = filtered_Y
    data.loc[start_index:(len(filtered_X)+start_index-1), 'Z'] = filtered_Z
    """

    # 可选的磁场等高线可视化（已注释）
    """
    plt.figure(figsize=(8, 6))
    levels = np.linspace(Z_LAB.min(), Z_LAB.max(), 10)
    plt.contour(Z_LAB, levels=levels, cmap='viridis')
    plt.title('Z分量磁场等高线')
    plt.xlabel('X轴')
    plt.ylabel('Y轴')
    plt.show()
    """

    # 可选的数据对比分析（已注释）
    """
    ori = []
    Lab = []
    for i in range(len(data['Lon'])):
        lon_ = data['Lon'][i]
        lat_ = data['Lat'][i]
        x_ = (lon_ - lon[0,0]) / (lon[839,839] - lon[0,0]) * 840.0
        y_ = (lat_ - lat[0,0]) / (lat[839,839] - lat[0,0]) * 840.0
        y_idx = int(round(x_))
        x_idx = int(round(y_))

        ori.append([data['X'][i], data['Y'][i], data['Z'][i]])
        Lab.append([X_LAB[x_idx, y_idx], Y_LAB[x_idx, y_idx], Z_LAB[x_idx, y_idx]])

    ori_np = np.array(ori)
    Lab_np = np.array(Lab)
    SegmentStatistics(ori_np, Lab_np)
    """
    
    # 检查文件路径
    if not file_paths:
        print("未提供文件路径")
        return

    # 调用主数据生成函数
    # ====================================================================
    # 参数说明:
    # file_paths[0]: 惯导数据文件路径
    # file_paths[1]: 卫导参考数据文件路径  
    # start_time, end_time: 时间范围
    # lon, lat: 磁图经纬度网格
    # X_LAB, Y_LAB, Z_LAB: 磁场三分量参考数据
    # data: 实测磁场数据
    # rgb_matrix: 磁场可视化背景图
    # data_dir: 输出目录
    # ====================================================================
    DataGeneration(file_paths[0], file_paths[1], start_time, end_time,
                  lon, lat, X_LAB, Y_LAB, Z_LAB, data, rgb_matrix, data_dir)
    
    # 可选：绘制轨迹可视化图
    # plot_trajectories(file_paths, start_time, end_time, rgb_matrix, lon, lat, data)

    # 生成完成后可视化一个样本
    visualize_sample(data_dir)


if __name__ == "__main__":
    main()